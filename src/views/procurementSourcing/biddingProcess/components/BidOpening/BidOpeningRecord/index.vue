<template>
	<div class="bid-opening-record">
		<StageTabs
			:tabs="tabs"
			v-model="activeTabIndex"
		/>
		<!-- 第一行：视图切换和开标信息 -->
		<div class="view-control">
			<div class="left-section">
				<el-radio-group
					v-model="viewMode"
					class="view-tabs"
				>
					<el-radio-button
						v-for="option in viewOptions"
						:key="option.value"
						:label="option.value"
					>
						{{ option.label }}
					</el-radio-button>
				</el-radio-group>
			</div>
			<div class="right-section">已发起 <span class="highlight">3</span> 轮开标</div>
		</div>

		<!-- 第二行：查询条件和导出按钮 -->
		<div class="search-section">
			<div class="left-section">
				<el-form
					:model="queryForm"
					inline
					class="search-form"
					label-width="145px"
				>
					<el-form-item label="开标轮次">
						<el-select
							v-model="queryForm.round"
							placeholder="请选择"
							class="search-select"
						>
							<el-option
								v-for="item in bidRounds"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</el-select>
					</el-form-item>
					<el-form-item label="物料名称">
						<el-input
							v-model="queryForm.materialName"
							placeholder="请输入物料名称搜索"
							clearable
							class="search-input"
						/>
					</el-form-item>
				</el-form>
			</div>
			<div class="right-section">
				<el-button @click="handleExport">
					导出
					<el-icon style="margin-left: 6px; color: #000"><Download /></el-icon>
				</el-button>
			</div>
		</div>

		<!-- 第三行：提示信息 -->
		<div class="alert-section">
			<img
				src="https://oss-public.yunlizhi.cn/frontend/fe-procurement-platform/error_warning.svg"
				alt=""
			/>
			<div>系统监测到2家供应商IP异常</div>
		</div>

		<!-- 第四行：表格 -->
		<el-table
			:data="tableDataForRender"
			class="bid-opening-table"
			:span-method="tableSpanMethod"
		>
			<el-table-column
				label="序号"
				width="60"
			>
				<template #default="{ row, $index }">
					<span v-if="row._rowSpan > 0">{{ row.displayIndex }}</span>
				</template>
			</el-table-column>
			<el-table-column label="物料编码">
				<template #default="{ row }">
					<span v-if="row._rowSpan > 0">{{ row.materialCode }}</span>
				</template>
			</el-table-column>
			<el-table-column label="物料名称">
				<template #default="{ row }">
					<span v-if="row._rowSpan > 0">{{ row.materialName }}</span>
				</template>
			</el-table-column>
			<el-table-column label="规格型号">
				<template #default="{ row }">
					<span v-if="row._rowSpan > 0">{{ row.specNo }}</span>
				</template>
			</el-table-column>
			<el-table-column label="需求数量">
				<template #default="{ row }">
					<span v-if="row._rowSpan > 0">{{ row.requiredQuantity }}</span>
				</template>
			</el-table-column>
			<el-table-column label="报价供应商数量">
				<template #default="{ row }">
					<span v-if="row._rowSpan > 0">{{ row.supplierCount }}</span>
				</template>
			</el-table-column>
			<el-table-column label="报价供应商">
				<template #default="{ row }">{{ row.supplier.name }}</template>
			</el-table-column>
			<el-table-column label="可供数量">
				<template #default="{ row }">{{ row.supplier.quantity }}</template>
			</el-table-column>
			<el-table-column label="出厂价">
				<template #default="{ row }">{{ row.supplier.price }}</template>
			</el-table-column>
			<el-table-column label="运费">
				<template #default="{ row }">{{ row.supplier.freight }}</template>
			</el-table-column>
			<el-table-column label="到厂价">
				<template #default="{ row }">{{ row.supplier.arrivalPrice }}</template>
			</el-table-column>
			<el-table-column label="总价">
				<template #default="{ row }">{{ row.supplier.total }}</template>
			</el-table-column>
			<el-table-column label="投标IP">
				<template #default="{ row }">
					<span :class="['supplier-cell', { 'ip-abnormal': row.supplier.ipAbnormal }]">{{ row.supplier.ip }}</span>
				</template>
			</el-table-column>
		</el-table>
	</div>
</template>

<script setup lang="ts">
import { ref, computed, defineExpose } from 'vue';
import StageTabs from '../../bidding/StageTabs.vue';
import { Plus, Download, Upload } from '@element-plus/icons-vue';

const tabs = [
	{ key: '1', label: '标段01: 物资采购物资采购...' },
	{ key: '2', label: '标段02: 物资采购物资' },
	{ key: '3', label: '标段03: 施工队伍安排' },
	{ key: '4', label: '标段04: 预算编制' },
];
const activeTabIndex = ref(0);

// Tab 切换相关
const viewMode = ref('material'); // 'material' | 'supplier'
const viewOptions = [
	{ label: '按物料查看', value: 'material' },
	{ label: '按供应商查看', value: 'supplier' },
];

// 开标轮次选项
const bidRounds = [
	{ label: '第一轮开标', value: 1 },
	{ label: '第二轮开标', value: 2 },
	{ label: '第三轮开标', value: 3 },
];

// 查询表单
interface QueryForm {
	round: number;
	materialName: string;
}

const queryForm = ref<QueryForm>({
	round: 1,
	materialName: '',
});

// 表格数据
interface SupplierBid {
	name: string;
	quantity: number;
	price: number;
	freight: number;
	arrivalPrice: number;
	total: number;
	ip: string;
	ipAbnormal?: boolean;
}

interface BidRecordRow {
	id: number;
	materialCode: string;
	materialName: string;
	specNo: string;
	requiredQuantity: number;
	suppliers: SupplierBid[];
}

interface RenderRow {
	displayIndex: number;
	materialCode: string;
	materialName: string;
	specNo: string;
	requiredQuantity: number;
	supplierCount: number;
	supplier: SupplierBid;
	_rowSpan: number;
}

const tableData = ref<BidRecordRow[]>([
	{
		id: 1,
		materialCode: 'ASW12345',
		materialName: '物料名称01',
		specNo: '物料名称01',
		requiredQuantity: 2000,
		suppliers: [
			{
				name: '上海申华控股股份有限公司',
				quantity: 10,
				price: 65949.21,
				freight: 65949.21,
				arrivalPrice: 65949.21,
				total: 65949.21,
				ip: '***********',
			},
			{
				name: '泛亚汽车技术中心有限公司',
				quantity: 10,
				price: 65949.21,
				freight: 65949.21,
				arrivalPrice: 65949.21,
				total: 65949.21,
				ip: '************',
				ipAbnormal: true,
			},
			{
				name: '采壹平(中国)投资有限公司',
				quantity: 10,
				price: 65949.21,
				freight: 65949.21,
				arrivalPrice: 65949.21,
				total: 65949.21,
				ip: '************',
				ipAbnormal: true,
			},
		],
	},
]);

// 拍平数据并加rowspan
const tableDataForRender = computed<RenderRow[]>(() => {
	let result: RenderRow[] = [];
	let displayIndex = 1;
	tableData.value.forEach((row) => {
		row.suppliers.forEach((supplier, idx) => {
			result.push({
				displayIndex,
				materialCode: row.materialCode,
				materialName: row.materialName,
				specNo: row.specNo,
				requiredQuantity: row.requiredQuantity,
				supplierCount: row.suppliers.length,
				supplier,
				_rowSpan: idx === 0 ? row.suppliers.length : 0,
			});
		});
		displayIndex++;
	});
	return result;
});

function tableSpanMethod({ row, column, rowIndex, columnIndex }: { row: RenderRow; column: any; rowIndex: number; columnIndex: number }) {
	// 左侧前6列合并
	if (columnIndex >= 0 && columnIndex <= 5) {
		return {
			rowspan: row._rowSpan,
			colspan: 1,
		};
	}
	return { rowspan: 1, colspan: 1 };
}

// 处理导出
function handleExport() {
	console.log('导出');
}

// 处理插入开标记录
function handleInsertBidRecord() {
	console.log('插入开标记录');
}

defineExpose({ tableDataForRender, tableSpanMethod });
</script>

<style scoped lang="scss">
@import '../../../styles/collapse-panel.scss';

.bid-opening-record {
	flex: 1;
	padding: 20px;
	.view-control {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 16px 0;

		.left-section {
			display: flex;
			align-items: center;
			gap: 40px;

			.view-tabs {
				:deep(.el-radio-button__inner) {
					padding: 8px 16px;
				}
			}
		}

		.right-section {
			color: var(--Color-Text-text-color-secondary, #86909c);
			font-family: 'PingFang SC';
			font-size: 12px;
			font-style: normal;
			font-weight: 400;
			line-height: 20px;
			.highlight {
				color: var(--Color-Text-text-color-primary, #1d2129);
				font-family: 'PingFang SC';
				font-size: 12px;
				font-style: normal;
				font-weight: 600;
				line-height: 20px;
			}
		}
	}

	.search-section {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 16px;

		:deep(.el-form .el-form-item) {
			margin-bottom: 0 !important;
		}

		.left-section {
			flex: 1;
		}
	}

	.alert-section {
		display: flex;
		padding: 8px 16px;
		align-items: center;
		gap: 8px;
		align-self: stretch;
		border-radius: var(--Radius-border-radius-small, 2px);
		background: var(--Color-Error-color-error-light-9, #ffede8);
		color: var(--Color-Text-text-color-primary, #1d2129);
		font-family: 'PingFang SC';
		font-size: 13px;
		font-style: normal;
		font-weight: 400;
		line-height: 22px;
	}
}

.bid-opening-table {
	:deep(.el-table__cell) {
		border-right: 1px solid #ebeef5;
	}
	:deep(.el-table__cell:last-child) {
		border-right: none !important;
	}
	:deep(.el-table__cell:first-child) {
		border-left: none !important;
	}
}
</style>
