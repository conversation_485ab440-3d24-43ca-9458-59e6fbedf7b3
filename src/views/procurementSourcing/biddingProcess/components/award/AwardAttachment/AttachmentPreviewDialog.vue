<template>
	<el-dialog
		v-model="visible"
		:title="`附件预览 - ${attachment?.name}`"
		width="900px"
		:before-close="handleClose"
	>
		<div class="attachment-preview">
			<!-- 附件信息 -->
			<div class="attachment-info">
				<el-descriptions :column="3" border>
					<el-descriptions-item label="附件名称">{{ attachment?.name }}</el-descriptions-item>
					<el-descriptions-item label="附件类型">
						<el-tag
							:type="getCategoryTagType(attachment?.category)"
							size="small"
						>
							{{ getCategoryText(attachment?.category) }}
						</el-tag>
					</el-descriptions-item>
					<el-descriptions-item label="文件大小">{{ formatFileSize(attachment?.size || 0) }}</el-descriptions-item>
					<el-descriptions-item label="上传人">{{ attachment?.uploader }}</el-descriptions-item>
					<el-descriptions-item label="上传时间">{{ attachment?.uploadTime }}</el-descriptions-item>
					<el-descriptions-item label="状态">
						<el-tag
							:type="getStatusTagType(attachment?.status)"
							size="small"
						>
							{{ getStatusText(attachment?.status) }}
						</el-tag>
					</el-descriptions-item>
				</el-descriptions>
			</div>

			<!-- 预览内容 -->
			<div class="preview-content">
				<div class="preview-header">
					<h4>文件预览</h4>
					<div class="preview-actions">
						<el-button
							type="primary"
							size="small"
							@click="handleDownload"
						>
							下载文件
						</el-button>
						<el-button
							type="success"
							size="small"
							@click="handleOpenExternal"
						>
							外部打开
						</el-button>
					</div>
				</div>

				<div class="preview-area">
					<!-- PDF预览 -->
					<div v-if="attachment?.type === 'pdf'" class="pdf-preview">
						<iframe
							:src="attachment.url"
							width="100%"
							height="500px"
							frameborder="0"
						></iframe>
					</div>

					<!-- 图片预览 -->
					<div v-else-if="isImageFile(attachment?.type)" class="image-preview">
						<el-image
							:src="attachment.url"
							:alt="attachment.name"
							fit="contain"
							style="width: 100%; max-height: 500px"
							:preview-src-list="[attachment.url]"
						/>
					</div>

					<!-- 文档预览（Word、Excel等） -->
					<div v-else-if="isDocumentFile(attachment?.type)" class="document-preview">
						<div class="preview-placeholder">
							<el-icon size="64" class="text-gray-400">
								<Document />
							</el-icon>
							<p class="text-gray-600">{{ attachment?.name }}</p>
							<p class="text-gray-500">此类型文件不支持在线预览，请下载后查看</p>
							<el-button
								type="primary"
								@click="handleDownload"
							>
								下载文件
							</el-button>
						</div>
					</div>

					<!-- 其他文件类型 -->
					<div v-else class="other-preview">
						<div class="preview-placeholder">
							<el-icon size="64" class="text-gray-400">
								<Folder />
							</el-icon>
							<p class="text-gray-600">{{ attachment?.name }}</p>
							<p class="text-gray-500">不支持预览此文件类型</p>
							<el-button
								type="primary"
								@click="handleDownload"
							>
								下载文件
							</el-button>
						</div>
					</div>
				</div>
			</div>

			<!-- 操作历史 -->
			<div class="operation-history">
				<div class="history-header">
					<h4>操作历史</h4>
				</div>
				<el-timeline size="small">
					<el-timeline-item
						v-for="(record, index) in operationHistory"
						:key="index"
						:timestamp="record.timestamp"
						:type="record.type"
					>
						<div class="history-content">
							<span class="operation-text">{{ record.operation }}</span>
							<span class="operator-text">by {{ record.operator }}</span>
						</div>
					</el-timeline-item>
				</el-timeline>
			</div>
		</div>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="handleClose">关闭</el-button>
				<el-button type="primary" @click="handleDownload">下载</el-button>
				<el-button type="danger" @click="handleDelete">删除</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Document, Folder } from '@element-plus/icons-vue';

interface Props {
	visible: boolean;
	attachment: any;
}

interface Emits {
	(e: 'update:visible', value: boolean): void;
	(e: 'delete', attachment: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(props.visible);

// 监听visible变化
watch(() => props.visible, (newVal) => {
	visible.value = newVal;
});

watch(visible, (newVal) => {
	emit('update:visible', newVal);
});

// 操作历史数据
const operationHistory = computed(() => {
	if (!props.attachment) return [];
	
	return [
		{
			timestamp: props.attachment.uploadTime,
			operation: '上传文件',
			operator: props.attachment.uploader,
			type: 'primary',
		},
		{
			timestamp: '2025-01-31 13:00',
			operation: '查看文件',
			operator: '李四',
			type: 'info',
		},
		{
			timestamp: '2025-01-31 14:30',
			operation: '下载文件',
			operator: '张三',
			type: 'success',
		},
	];
});

// 获取类别标签类型
function getCategoryTagType(category?: string): string {
	const typeMap: Record<string, string> = {
		decision: 'success',
		report: 'primary',
		notice: 'warning',
		contract: 'info',
		other: '',
	};
	return typeMap[category || ''] || '';
}

// 获取类别文本
function getCategoryText(category?: string): string {
	const textMap: Record<string, string> = {
		decision: '定标决定',
		report: '评标报告',
		notice: '通知文件',
		contract: '合同文件',
		other: '其他',
	};
	return textMap[category || ''] || '未知';
}

// 获取状态标签类型
function getStatusTagType(status?: string): string {
	const typeMap: Record<string, string> = {
		active: 'success',
		inactive: 'info',
		deleted: 'danger',
	};
	return typeMap[status || ''] || '';
}

// 获取状态文本
function getStatusText(status?: string): string {
	const textMap: Record<string, string> = {
		active: '有效',
		inactive: '无效',
		deleted: '已删除',
	};
	return textMap[status || ''] || '未知';
}

// 判断是否为图片文件
function isImageFile(type?: string): boolean {
	const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
	return imageTypes.includes(type?.toLowerCase() || '');
}

// 判断是否为文档文件
function isDocumentFile(type?: string): boolean {
	const documentTypes = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];
	return documentTypes.includes(type?.toLowerCase() || '');
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
	if (bytes === 0) return '0 B';
	const k = 1024;
	const sizes = ['B', 'KB', 'MB', 'GB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 处理关闭
function handleClose() {
	visible.value = false;
}

// 处理下载
function handleDownload() {
	if (props.attachment?.url) {
		window.open(props.attachment.url, '_blank');
		ElMessage.success('开始下载文件');
	}
}

// 处理外部打开
function handleOpenExternal() {
	if (props.attachment?.url) {
		window.open(props.attachment.url, '_blank');
	}
}

// 处理删除
async function handleDelete() {
	try {
		await ElMessageBox.confirm(
			`确定要删除附件"${props.attachment?.name}"吗？`,
			'确认删除',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}
		);

		emit('delete', props.attachment);
		handleClose();
	} catch {
		// 用户取消
	}
}
</script>

<style lang="scss" scoped>
.attachment-preview {
	.attachment-info {
		margin-bottom: 24px;
	}

	.preview-content {
		margin-bottom: 24px;

		.preview-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16px;

			h4 {
				margin: 0;
				font-size: 14px;
				font-weight: 600;
				color: #1d2129;
			}

			.preview-actions {
				display: flex;
				gap: 8px;
			}
		}

		.preview-area {
			border: 1px solid #e5e7eb;
			border-radius: 6px;
			overflow: hidden;

			.pdf-preview,
			.image-preview {
				background: #f9fafb;
			}

			.document-preview,
			.other-preview {
				.preview-placeholder {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					padding: 60px 20px;
					background: #f9fafb;

					p {
						margin: 8px 0;
						text-align: center;
					}

					.text-gray-600 {
						color: #4b5563;
						font-weight: 500;
					}

					.text-gray-500 {
						color: #6b7280;
						font-size: 14px;
					}

					.text-gray-400 {
						color: #9ca3af;
					}
				}
			}
		}
	}

	.operation-history {
		.history-header {
			margin-bottom: 16px;

			h4 {
				margin: 0;
				font-size: 14px;
				font-weight: 600;
				color: #1d2129;
			}
		}

		.history-content {
			.operation-text {
				font-weight: 500;
				color: #1d2129;
			}

			.operator-text {
				margin-left: 8px;
				font-size: 12px;
				color: #6b7280;
			}
		}
	}
}

.dialog-footer {
	text-align: right;
}
</style>
