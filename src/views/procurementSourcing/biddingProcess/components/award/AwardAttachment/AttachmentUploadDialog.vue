<template>
	<el-dialog
		v-model="visible"
		title="上传附件"
		width="600px"
		:before-close="handleClose"
	>
		<div class="upload-content">
			<el-form
				ref="formRef"
				:model="uploadForm"
				:rules="formRules"
				label-width="100px"
			>
				<el-form-item label="附件类型" prop="category">
					<el-select
						v-model="uploadForm.category"
						placeholder="请选择附件类型"
						style="width: 100%"
					>
						<el-option label="定标决定" value="decision" />
						<el-option label="评标报告" value="report" />
						<el-option label="通知文件" value="notice" />
						<el-option label="合同文件" value="contract" />
						<el-option label="其他" value="other" />
					</el-select>
				</el-form-item>

				<el-form-item label="附件名称" prop="name">
					<el-input
						v-model="uploadForm.name"
						placeholder="请输入附件名称"
						maxlength="100"
						show-word-limit
					/>
				</el-form-item>

				<el-form-item label="附件描述">
					<el-input
						v-model="uploadForm.description"
						type="textarea"
						:rows="3"
						placeholder="请输入附件描述（选填）"
						maxlength="200"
						show-word-limit
					/>
				</el-form-item>

				<el-form-item label="选择文件" prop="files">
					<el-upload
						ref="uploadRef"
						class="upload-demo"
						:auto-upload="false"
						:on-change="handleFileChange"
						:on-remove="handleFileRemove"
						:file-list="fileList"
						:limit="5"
						multiple
						accept=".pdf,.doc,.docx,.xls,.xlsx,.txt,.jpg,.jpeg,.png,.gif"
					>
						<el-button type="primary">选择文件</el-button>
						<template #tip>
							<div class="el-upload__tip">
								支持 PDF、Word、Excel、TXT、图片格式，单个文件不超过 20MB，最多上传5个文件
							</div>
						</template>
					</el-upload>
				</el-form-item>

				<el-form-item label="上传选项">
					<el-checkbox-group v-model="uploadOptions">
						<el-checkbox label="auto_rename">自动重命名重复文件</el-checkbox>
						<el-checkbox label="compress">自动压缩大文件</el-checkbox>
						<el-checkbox label="backup">创建备份副本</el-checkbox>
					</el-checkbox-group>
				</el-form-item>
			</el-form>

			<!-- 文件预览列表 -->
			<div v-if="fileList.length > 0" class="file-preview">
				<h4>待上传文件</h4>
				<el-table
					:data="fileList"
					style="width: 100%"
					size="small"
				>
					<el-table-column label="文件名" prop="name" min-width="200" />
					<el-table-column label="大小" width="100">
						<template #default="{ row }">
							{{ formatFileSize(row.size || 0) }}
						</template>
					</el-table-column>
					<el-table-column label="类型" width="80">
						<template #default="{ row }">
							{{ getFileExtension(row.name) }}
						</template>
					</el-table-column>
					<el-table-column label="状态" width="100">
						<template #default="{ row }">
							<el-tag
								:type="getFileStatusType(row)"
								size="small"
							>
								{{ getFileStatusText(row) }}
							</el-tag>
						</template>
					</el-table-column>
				</el-table>
			</div>
		</div>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="handleClose">取消</el-button>
				<el-button
					type="primary"
					:loading="uploading"
					:disabled="fileList.length === 0"
					@click="handleUpload"
				>
					上传 ({{ fileList.length }})
				</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import type { UploadFile, UploadFiles } from 'element-plus';

interface Props {
	visible: boolean;
}

interface Emits {
	(e: 'update:visible', value: boolean): void;
	(e: 'upload-success', files: any[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(props.visible);
const uploading = ref(false);

// 表单引用
const formRef = ref();
const uploadRef = ref();

// 上传表单
const uploadForm = reactive({
	category: '',
	name: '',
	description: '',
});

// 上传选项
const uploadOptions = ref(['auto_rename']);

// 文件列表
const fileList = ref<UploadFiles>([]);

// 表单验证规则
const formRules = {
	category: [
		{ required: true, message: '请选择附件类型', trigger: 'change' },
	],
	name: [
		{ required: true, message: '请输入附件名称', trigger: 'blur' },
		{ max: 100, message: '附件名称不能超过100个字符', trigger: 'blur' },
	],
	files: [
		{ required: true, message: '请选择要上传的文件', trigger: 'change' },
	],
};

// 监听visible变化
watch(() => props.visible, (newVal) => {
	visible.value = newVal;
	if (newVal) {
		// 重置表单
		resetForm();
	}
});

watch(visible, (newVal) => {
	emit('update:visible', newVal);
});

// 重置表单
function resetForm() {
	uploadForm.category = '';
	uploadForm.name = '';
	uploadForm.description = '';
	uploadOptions.value = ['auto_rename'];
	fileList.value = [];
	formRef.value?.clearValidate();
}

// 处理文件变化
function handleFileChange(file: UploadFile, files: UploadFiles) {
	// 验证文件大小
	if (file.size && file.size > 20 * 1024 * 1024) {
		ElMessage.error(`文件 ${file.name} 大小超过 20MB`);
		files.splice(files.indexOf(file), 1);
		return false;
	}

	// 验证文件类型
	const allowedTypes = [
		'application/pdf',
		'application/msword',
		'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
		'application/vnd.ms-excel',
		'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
		'text/plain',
		'image/jpeg',
		'image/jpg',
		'image/png',
		'image/gif',
	];

	if (file.raw && !allowedTypes.includes(file.raw.type)) {
		ElMessage.error(`不支持的文件格式: ${file.name}`);
		files.splice(files.indexOf(file), 1);
		return false;
	}

	// 自动填充附件名称（如果只有一个文件且名称为空）
	if (files.length === 1 && !uploadForm.name) {
		uploadForm.name = file.name?.replace(/\.[^/.]+$/, '') || ''; // 移除文件扩展名
	}

	return true;
}

// 处理文件移除
function handleFileRemove(file: UploadFile, files: UploadFiles) {
	// 如果移除后没有文件了，清空名称
	if (files.length === 0) {
		uploadForm.name = '';
	}
}

// 获取文件扩展名
function getFileExtension(filename: string): string {
	return filename.split('.').pop()?.toUpperCase() || '';
}

// 获取文件状态类型
function getFileStatusType(file: UploadFile): string {
	if (file.size && file.size > 20 * 1024 * 1024) {
		return 'danger';
	}
	return 'success';
}

// 获取文件状态文本
function getFileStatusText(file: UploadFile): string {
	if (file.size && file.size > 20 * 1024 * 1024) {
		return '文件过大';
	}
	return '准备就绪';
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
	if (bytes === 0) return '0 B';
	const k = 1024;
	const sizes = ['B', 'KB', 'MB', 'GB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 处理关闭
function handleClose() {
	visible.value = false;
}

// 处理上传
async function handleUpload() {
	try {
		await formRef.value?.validate();

		uploading.value = true;

		// 模拟上传过程
		await new Promise(resolve => setTimeout(resolve, 2000));

		// 构造上传成功的文件信息
		const uploadedFiles = fileList.value.map(file => ({
			name: uploadForm.name || file.name,
			category: uploadForm.category,
			description: uploadForm.description,
			type: getFileExtension(file.name || '').toLowerCase(),
			size: file.size || 0,
			url: `/documents/${file.name}`, // 模拟URL
			originalName: file.name,
		}));

		emit('upload-success', uploadedFiles);
		ElMessage.success(`成功上传 ${uploadedFiles.length} 个文件`);
		handleClose();
	} catch (error) {
		console.error('表单验证失败:', error);
	} finally {
		uploading.value = false;
	}
}
</script>

<style lang="scss" scoped>
.upload-content {
	.upload-demo {
		width: 100%;
	}

	.file-preview {
		margin-top: 20px;
		padding-top: 20px;
		border-top: 1px solid #e5e7eb;

		h4 {
			margin: 0 0 12px 0;
			font-size: 14px;
			font-weight: 600;
			color: #1d2129;
		}
	}
}

.dialog-footer {
	text-align: right;
}

:deep(.el-upload__tip) {
	margin-top: 8px;
	font-size: 12px;
	color: #6b7280;
}
</style>
