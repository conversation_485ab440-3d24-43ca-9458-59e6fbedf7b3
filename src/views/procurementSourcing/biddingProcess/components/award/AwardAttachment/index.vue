<template>
	<div class="award-attachment-container">
		<div class="attachment-content">
			<!-- 定标附件管理 -->
			<div class="attachment-section">
				<div class="section-header">
					<h3>定标附件</h3>
					<div class="header-actions">
						<el-button
							type="primary"
							size="small"
							@click="handleUpload"
						>
							上传附件
						</el-button>
						<el-button
							type="success"
							size="small"
							@click="handleBatchDownload"
							:disabled="selectedAttachments.length === 0"
						>
							批量下载
						</el-button>
					</div>
				</div>

				<!-- 附件列表 -->
				<div class="attachment-table">
					<el-table
						ref="tableRef"
						:data="attachmentList"
						style="width: 100%"
						border
						stripe
						@selection-change="handleSelectionChange"
					>
						<el-table-column
							type="selection"
							width="55"
							align="center"
						/>
						<el-table-column
							label="序号"
							type="index"
							width="60"
							align="center"
						/>
						<el-table-column
							label="附件名称"
							prop="name"
							min-width="200"
						>
							<template #default="{ row }">
								<div class="file-info">
									<el-icon class="file-icon">
										<component :is="getFileIcon(row.type)" />
									</el-icon>
									<span class="file-name">{{ row.name }}</span>
								</div>
							</template>
						</el-table-column>
						<el-table-column
							label="附件类型"
							prop="category"
							width="120"
							align="center"
						>
							<template #default="{ row }">
								<el-tag
									:type="getCategoryTagType(row.category)"
									size="small"
								>
									{{ getCategoryText(row.category) }}
								</el-tag>
							</template>
						</el-table-column>
						<el-table-column
							label="文件大小"
							prop="size"
							width="100"
							align="center"
						>
							<template #default="{ row }">
								{{ formatFileSize(row.size) }}
							</template>
						</el-table-column>
						<el-table-column
							label="上传人"
							prop="uploader"
							width="100"
							align="center"
						/>
						<el-table-column
							label="上传时间"
							prop="uploadTime"
							width="150"
							align="center"
						/>
						<el-table-column
							label="状态"
							prop="status"
							width="100"
							align="center"
						>
							<template #default="{ row }">
								<el-tag
									:type="getStatusTagType(row.status)"
									size="small"
								>
									{{ getStatusText(row.status) }}
								</el-tag>
							</template>
						</el-table-column>
						<el-table-column
							label="操作"
							width="200"
							align="center"
							fixed="right"
						>
							<template #default="{ row }">
								<el-button
									type="primary"
									size="small"
									@click="handlePreview(row)"
								>
									预览
								</el-button>
								<el-button
									type="success"
									size="small"
									@click="handleDownload(row)"
								>
									下载
								</el-button>
								<el-button
									type="danger"
									size="small"
									@click="handleDelete(row)"
								>
									删除
								</el-button>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</div>

			<!-- 附件统计 -->
			<div class="statistics-section">
				<div class="section-header">
					<h3>附件统计</h3>
				</div>

				<el-row :gutter="20">
					<el-col :span="6">
						<el-card class="stat-card">
							<div class="stat-content">
								<div class="stat-number">{{ attachmentList.length }}</div>
								<div class="stat-label">总附件数</div>
							</div>
						</el-card>
					</el-col>
					<el-col :span="6">
						<el-card class="stat-card">
							<div class="stat-content">
								<div class="stat-number">{{ getTotalSize() }}</div>
								<div class="stat-label">总大小</div>
							</div>
						</el-card>
					</el-col>
					<el-col :span="6">
						<el-card class="stat-card">
							<div class="stat-content">
								<div class="stat-number">{{ getActiveCount() }}</div>
								<div class="stat-label">有效附件</div>
							</div>
						</el-card>
					</el-col>
					<el-col :span="6">
						<el-card class="stat-card">
							<div class="stat-content">
								<div class="stat-number">{{ getTypeCount() }}</div>
								<div class="stat-label">附件类型</div>
							</div>
						</el-card>
					</el-col>
				</el-row>
			</div>
		</div>

		<!-- 附件上传弹窗 -->
		<AttachmentUploadDialog
			v-model:visible="uploadDialogVisible"
			@upload-success="handleUploadSuccess"
		/>

		<!-- 附件预览弹窗 -->
		<AttachmentPreviewDialog
			v-model:visible="previewDialogVisible"
			:attachment="currentAttachment"
		/>
	</div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Document, Picture, VideoPlay, Folder, Download } from '@element-plus/icons-vue';
import AttachmentUploadDialog from './AttachmentUploadDialog.vue';
import AttachmentPreviewDialog from './AttachmentPreviewDialog.vue';

// 表格引用
const tableRef = ref();

// 弹窗状态
const uploadDialogVisible = ref(false);
const previewDialogVisible = ref(false);

// 当前附件
const currentAttachment = ref(null);

// 选中的附件
const selectedAttachments = ref([]);

// 附件列表数据
const attachmentList = ref([
	{
		id: '1',
		name: '定标决定书.pdf',
		category: 'decision',
		type: 'pdf',
		size: 1024 * 1024 * 2.5,
		uploader: '王小二',
		uploadTime: '2025-01-31 12:30',
		status: 'active',
		url: '/documents/award-decision.pdf',
	},
	{
		id: '2',
		name: '评标报告.pdf',
		category: 'report',
		type: 'pdf',
		size: 1024 * 1024 * 5.2,
		uploader: '李四',
		uploadTime: '2025-01-30 17:00',
		status: 'active',
		url: '/documents/evaluation-report.pdf',
	},
	{
		id: '3',
		name: '中标通知书.docx',
		category: 'notice',
		type: 'docx',
		size: 1024 * 512,
		uploader: '张三',
		uploadTime: '2025-01-31 14:00',
		status: 'active',
		url: '/documents/award-notice.docx',
	},
]);

// 获取文件图标
function getFileIcon(type: string) {
	const iconMap: Record<string, any> = {
		pdf: Document,
		doc: Document,
		docx: Document,
		xls: Document,
		xlsx: Document,
		jpg: Picture,
		jpeg: Picture,
		png: Picture,
		gif: Picture,
		mp4: VideoPlay,
		avi: VideoPlay,
		default: Folder,
	};
	return iconMap[type] || iconMap.default;
}

// 获取类别标签类型
function getCategoryTagType(category: string): string {
	const typeMap: Record<string, string> = {
		decision: 'success',
		report: 'primary',
		notice: 'warning',
		contract: 'info',
		other: '',
	};
	return typeMap[category] || '';
}

// 获取类别文本
function getCategoryText(category: string): string {
	const textMap: Record<string, string> = {
		decision: '定标决定',
		report: '评标报告',
		notice: '通知文件',
		contract: '合同文件',
		other: '其他',
	};
	return textMap[category] || '未知';
}

// 获取状态标签类型
function getStatusTagType(status: string): string {
	const typeMap: Record<string, string> = {
		active: 'success',
		inactive: 'info',
		deleted: 'danger',
	};
	return typeMap[status] || '';
}

// 获取状态文本
function getStatusText(status: string): string {
	const textMap: Record<string, string> = {
		active: '有效',
		inactive: '无效',
		deleted: '已删除',
	};
	return textMap[status] || '未知';
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
	if (bytes === 0) return '0 B';
	const k = 1024;
	const sizes = ['B', 'KB', 'MB', 'GB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 获取总大小
function getTotalSize(): string {
	const total = attachmentList.value.reduce((sum, item) => sum + item.size, 0);
	return formatFileSize(total);
}

// 获取有效附件数
function getActiveCount(): number {
	return attachmentList.value.filter(item => item.status === 'active').length;
}

// 获取附件类型数
function getTypeCount(): number {
	const types = new Set(attachmentList.value.map(item => item.category));
	return types.size;
}

// 处理选择变化
function handleSelectionChange(selection: any[]) {
	selectedAttachments.value = selection;
}

// 处理上传
function handleUpload() {
	uploadDialogVisible.value = true;
}

// 处理上传成功
function handleUploadSuccess(file: any) {
	attachmentList.value.push({
		id: Date.now().toString(),
		name: file.name,
		category: file.category || 'other',
		type: file.type || 'unknown',
		size: file.size,
		uploader: '当前用户',
		uploadTime: new Date().toLocaleString(),
		status: 'active',
		url: file.url,
	});
	ElMessage.success('附件上传成功');
}

// 处理预览
function handlePreview(attachment: any) {
	currentAttachment.value = attachment;
	previewDialogVisible.value = true;
}

// 处理下载
function handleDownload(attachment: any) {
	window.open(attachment.url, '_blank');
}

// 处理删除
async function handleDelete(attachment: any) {
	try {
		await ElMessageBox.confirm(
			`确定要删除附件"${attachment.name}"吗？`,
			'确认删除',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}
		);

		const index = attachmentList.value.findIndex(item => item.id === attachment.id);
		if (index > -1) {
			attachmentList.value.splice(index, 1);
			ElMessage.success('删除成功');
		}
	} catch {
		// 用户取消
	}
}

// 处理批量下载
function handleBatchDownload() {
	if (selectedAttachments.value.length === 0) {
		ElMessage.warning('请先选择要下载的附件');
		return;
	}

	// 实现批量下载逻辑
	selectedAttachments.value.forEach((attachment: any) => {
		window.open(attachment.url, '_blank');
	});

	ElMessage.success(`开始下载 ${selectedAttachments.value.length} 个附件`);
}
</script>

<style lang="scss" scoped>
.award-attachment-container {
	padding: 20px;
	background: #fff;
	border-radius: 6px;
}

.attachment-content {
	.attachment-section,
	.statistics-section {
		margin-bottom: 32px;
		
		.section-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16px;
			
			h3 {
				margin: 0;
				font-size: 16px;
				font-weight: 600;
				color: var(--Color-Text-text-color-primary, #1d2129);
			}
			
			.header-actions {
				display: flex;
				gap: 8px;
			}
		}
	}

	.attachment-table {
		.file-info {
			display: flex;
			align-items: center;
			gap: 8px;

			.file-icon {
				color: #0069ff;
			}

			.file-name {
				flex: 1;
			}
		}
	}

	.statistics-section {
		.stat-card {
			.stat-content {
				text-align: center;
				padding: 10px 0;

				.stat-number {
					font-size: 24px;
					font-weight: 600;
					color: #0069ff;
					margin-bottom: 8px;
				}

				.stat-label {
					font-size: 14px;
					color: #6b7280;
				}
			}
		}
	}
}
</style>
