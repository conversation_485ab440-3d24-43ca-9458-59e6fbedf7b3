<template>
	<el-dialog
		v-model="visible"
		title="历史报价"
		width="900px"
		:before-close="handleClose"
	>
		<div class="history-price-content">
			<!-- 标的信息 -->
			<div class="bid-item-info">
				<h4>标的信息</h4>
				<el-descriptions :column="3" border>
					<el-descriptions-item label="标的编号">{{ bidItem?.bidItemCode }}</el-descriptions-item>
					<el-descriptions-item label="物料名称">{{ bidItem?.materialName }}</el-descriptions-item>
					<el-descriptions-item label="规格型号">{{ bidItem?.specification }}</el-descriptions-item>
					<el-descriptions-item label="需求数量">{{ bidItem?.quantity }}</el-descriptions-item>
					<el-descriptions-item label="计划价格">{{ formatPrice(bidItem?.plannedPrice || 0) }}</el-descriptions-item>
				</el-descriptions>
			</div>

			<!-- 历史报价表格 -->
			<div class="history-table">
				<h4>历史报价记录</h4>
				<el-table
					:data="historyPriceData"
					style="width: 100%"
					border
					stripe
				>
					<el-table-column
						label="序号"
						type="index"
						width="60"
						align="center"
					/>
					<el-table-column
						label="供应商名称"
						prop="supplierName"
						min-width="200"
					/>
					<el-table-column
						label="报价时间"
						prop="bidTime"
						width="150"
						align="center"
					/>
					<el-table-column
						label="报价金额"
						prop="bidPrice"
						width="120"
						align="center"
					>
						<template #default="{ row }">
							<span class="price-text">{{ formatPrice(row.bidPrice) }}</span>
						</template>
					</el-table-column>
					<el-table-column
						label="价格变化"
						width="120"
						align="center"
					>
						<template #default="{ row, $index }">
							<span
								v-if="$index > 0"
								:class="getPriceChangeClass(row, $index)"
							>
								{{ getPriceChangeText(row, $index) }}
							</span>
							<span v-else class="text-gray-500">-</span>
						</template>
					</el-table-column>
					<el-table-column
						label="报价状态"
						prop="status"
						width="100"
						align="center"
					>
						<template #default="{ row }">
							<el-tag
								:type="getStatusTagType(row.status)"
								size="small"
							>
								{{ getStatusText(row.status) }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column
						label="备注"
						prop="remark"
						min-width="150"
					>
						<template #default="{ row }">
							<span class="text-gray-600">{{ row.remark || '-' }}</span>
						</template>
					</el-table-column>
				</el-table>
			</div>

			<!-- 价格趋势图 -->
			<div class="price-trend">
				<h4>价格趋势</h4>
				<div class="chart-container">
					<div class="chart-placeholder">
						<el-icon size="48" class="text-gray-400">
							<DataLine />
						</el-icon>
						<p class="text-gray-500">价格趋势图（功能开发中）</p>
					</div>
				</div>
			</div>
		</div>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="handleClose">关闭</el-button>
				<el-button type="primary" @click="handleExport">导出记录</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { DataLine } from '@element-plus/icons-vue';
import type { BidItem } from './types';

interface Props {
	visible: boolean;
	bidItem: BidItem | null;
}

interface Emits {
	(e: 'update:visible', value: boolean): void;
}

interface HistoryPriceRecord {
	id: string;
	supplierName: string;
	bidTime: string;
	bidPrice: number;
	status: 'submitted' | 'modified' | 'final';
	remark?: string;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(props.visible);

// 监听visible变化
watch(() => props.visible, (newVal) => {
	visible.value = newVal;
});

watch(visible, (newVal) => {
	emit('update:visible', newVal);
});

// 模拟历史报价数据
const historyPriceData = computed<HistoryPriceRecord[]>(() => {
	if (!props.bidItem) return [];
	
	// 模拟每个供应商的历史报价记录
	const records: HistoryPriceRecord[] = [];
	
	props.bidItem.suppliers.forEach((supplier, index) => {
		// 每个供应商生成2-3条历史记录
		const recordCount = 2 + Math.floor(Math.random() * 2);
		
		for (let i = 0; i < recordCount; i++) {
			const basePrice = supplier.bidPrice;
			const priceVariation = (Math.random() - 0.5) * 20; // ±10的价格波动
			const price = i === recordCount - 1 ? basePrice : basePrice + priceVariation;
			
			records.push({
				id: `${supplier.id}-${i}`,
				supplierName: supplier.name,
				bidTime: `2025-01-${String(15 + i).padStart(2, '0')} ${String(9 + i).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}:00`,
				bidPrice: price,
				status: i === recordCount - 1 ? 'final' : (i === 0 ? 'submitted' : 'modified'),
				remark: i === recordCount - 1 ? '最终报价' : (i === 0 ? '初次报价' : '修改报价'),
			});
		}
	});
	
	// 按时间排序
	return records.sort((a, b) => new Date(a.bidTime).getTime() - new Date(b.bidTime).getTime());
});

// 格式化价格
function formatPrice(price: number): string {
	return `¥${price.toFixed(2)}`;
}

// 获取价格变化文本
function getPriceChangeText(row: HistoryPriceRecord, index: number): string {
	if (index === 0) return '-';
	
	const prevPrice = historyPriceData.value[index - 1]?.bidPrice || 0;
	const currentPrice = row.bidPrice;
	const change = currentPrice - prevPrice;
	
	if (change > 0) {
		return `+${formatPrice(change)}`;
	} else if (change < 0) {
		return `${formatPrice(change)}`;
	} else {
		return '无变化';
	}
}

// 获取价格变化样式类
function getPriceChangeClass(row: HistoryPriceRecord, index: number): string {
	if (index === 0) return '';
	
	const prevPrice = historyPriceData.value[index - 1]?.bidPrice || 0;
	const currentPrice = row.bidPrice;
	const change = currentPrice - prevPrice;
	
	if (change > 0) {
		return 'text-red-500'; // 价格上涨，红色
	} else if (change < 0) {
		return 'text-green-500'; // 价格下降，绿色
	} else {
		return 'text-gray-500'; // 无变化，灰色
	}
}

// 获取状态标签类型
function getStatusTagType(status: string): string {
	switch (status) {
		case 'submitted':
			return 'info';
		case 'modified':
			return 'warning';
		case 'final':
			return 'success';
		default:
			return '';
	}
}

// 获取状态文本
function getStatusText(status: string): string {
	switch (status) {
		case 'submitted':
			return '已提交';
		case 'modified':
			return '已修改';
		case 'final':
			return '最终报价';
		default:
			return '未知';
	}
}

// 处理关闭
function handleClose() {
	visible.value = false;
}

// 处理导出
function handleExport() {
	console.log('导出历史报价记录');
	// 实现导出逻辑
}
</script>

<style lang="scss" scoped>
.history-price-content {
	.bid-item-info,
	.history-table,
	.price-trend {
		margin-bottom: 24px;
		
		h4 {
			margin: 0 0 12px 0;
			font-size: 14px;
			font-weight: 600;
			color: var(--Color-Text-text-color-primary, #1d2129);
		}
	}
	
	.price-text {
		font-weight: 500;
		color: var(--Color-Primary-color-primary, #0069ff);
	}
	
	.chart-container {
		height: 200px;
		border: 1px solid #e5e7eb;
		border-radius: 6px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: #fafafa;
		
		.chart-placeholder {
			text-align: center;
			
			p {
				margin: 8px 0 0 0;
				font-size: 14px;
			}
		}
	}
}

.dialog-footer {
	text-align: right;
}

// 工具类样式
.text-red-500 {
	color: #ef4444;
}

.text-green-500 {
	color: #10b981;
}

.text-gray-500 {
	color: #6b7280;
}

.text-gray-400 {
	color: #9ca3af;
}

.text-gray-600 {
	color: #4b5563;
}
</style>
