<template>
	<el-dialog
		v-model="visible"
		title="选择中标供应商"
		width="800px"
		:before-close="handleClose"
	>
		<div class="winner-selection-content">
			<!-- 标的信息 -->
			<div class="bid-item-info">
				<h4>标的信息</h4>
				<el-descriptions :column="2" border>
					<el-descriptions-item label="标的编号">{{ bidItem?.bidItemCode }}</el-descriptions-item>
					<el-descriptions-item label="物料名称">{{ bidItem?.materialName }}</el-descriptions-item>
					<el-descriptions-item label="规格型号">{{ bidItem?.specification }}</el-descriptions-item>
					<el-descriptions-item label="需求数量">{{ bidItem?.quantity }}</el-descriptions-item>
					<el-descriptions-item label="计划价格">{{ formatPrice(bidItem?.plannedPrice || 0) }}</el-descriptions-item>
				</el-descriptions>
			</div>

			<!-- 供应商选择 -->
			<div class="supplier-selection">
				<h4>投标供应商</h4>
				<el-table
					:data="bidItem?.suppliers || []"
					style="width: 100%"
					@selection-change="handleSelectionChange"
				>
					<el-table-column
						type="radio"
						width="55"
						align="center"
					>
						<template #default="{ row }">
							<el-radio
								v-model="selectedSupplierId"
								:label="row.id"
								@change="handleRadioChange(row.id)"
							>
								&nbsp;
							</el-radio>
						</template>
					</el-table-column>
					<el-table-column
						label="供应商名称"
						prop="name"
						min-width="200"
					/>
					<el-table-column
						label="投标价格"
						prop="bidPrice"
						width="120"
						align="center"
					>
						<template #default="{ row }">
							<span class="price-text">{{ formatPrice(row.bidPrice) }}</span>
						</template>
					</el-table-column>
					<el-table-column
						label="价格排名"
						width="100"
						align="center"
					>
						<template #default="{ row, $index }">
							<el-tag
								:type="getRankTagType($index)"
								size="small"
							>
								第{{ $index + 1 }}名
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column
						label="当前状态"
						width="100"
						align="center"
					>
						<template #default="{ row }">
							<el-tag
								v-if="row.isWinner"
								type="success"
								size="small"
							>
								已中标
							</el-tag>
							<span v-else class="text-gray-500">未中标</span>
						</template>
					</el-table-column>
				</el-table>
			</div>

			<!-- 定标说明 -->
			<div class="award-remark">
				<h4>定标说明</h4>
				<el-input
					v-model="awardRemark"
					type="textarea"
					:rows="4"
					placeholder="请输入定标说明（选填）"
					maxlength="500"
					show-word-limit
				/>
			</div>
		</div>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="handleClose">取消</el-button>
				<el-button
					type="primary"
					:disabled="!selectedSupplierId"
					@click="handleConfirm"
				>
					确认定标
				</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import type { BidItem } from './types';

interface Props {
	visible: boolean;
	bidItem: BidItem | null;
}

interface Emits {
	(e: 'update:visible', value: boolean): void;
	(e: 'confirm', supplierId: string, remark?: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(props.visible);
const selectedSupplierId = ref<string>('');
const awardRemark = ref<string>('');

// 监听visible变化
watch(() => props.visible, (newVal) => {
	visible.value = newVal;
	if (newVal) {
		// 重置表单
		selectedSupplierId.value = '';
		awardRemark.value = '';
		// 如果已有中标供应商，预选中
		const winner = props.bidItem?.suppliers.find(s => s.isWinner);
		if (winner) {
			selectedSupplierId.value = winner.id;
		}
	}
});

watch(visible, (newVal) => {
	emit('update:visible', newVal);
});

// 格式化价格
function formatPrice(price: number): string {
	return `¥${price.toFixed(2)}`;
}

// 获取排名标签类型
function getRankTagType(index: number): string {
	if (index === 0) return 'success';
	if (index === 1) return 'warning';
	if (index === 2) return 'info';
	return '';
}

// 处理单选框变化
function handleRadioChange(supplierId: string) {
	selectedSupplierId.value = supplierId;
}

// 处理选择变化
function handleSelectionChange(selection: any[]) {
	// 单选逻辑已在radio中处理
}

// 处理关闭
function handleClose() {
	visible.value = false;
}

// 处理确认
function handleConfirm() {
	if (selectedSupplierId.value) {
		emit('confirm', selectedSupplierId.value, awardRemark.value);
	}
}
</script>

<style lang="scss" scoped>
.winner-selection-content {
	.bid-item-info,
	.supplier-selection,
	.award-remark {
		margin-bottom: 24px;
		
		h4 {
			margin: 0 0 12px 0;
			font-size: 14px;
			font-weight: 600;
			color: var(--Color-Text-text-color-primary, #1d2129);
		}
	}
	
	.price-text {
		font-weight: 500;
		color: var(--Color-Primary-color-primary, #0069ff);
	}
}

.dialog-footer {
	text-align: right;
}

:deep(.el-radio) {
	.el-radio__input {
		line-height: 1;
	}
}
</style>
