<template>
	<div class="award-decision-container">
		<!-- 标段选择器 -->
		<div class="stage-tabs-wrapper">
			<StageTabs
				v-model="activeTabIndex"
				:tabs="tabs"
			/>
		</div>

		<!-- 定标清单内容 -->
		<div class="award-content">
			<!-- 定标信息 -->
			<div class="award-info-section">
				<div class="section-header">
					<h3>定标清单</h3>
					<div class="award-actions">
						<el-button
							type="primary"
							size="small"
							@click="handleExport"
						>
							导出
						</el-button>
					</div>
				</div>

				<!-- 定标表格 -->
				<div class="award-table">
					<el-table
						:data="awardTableData"
						style="width: 100%"
						border
						stripe
					>
						<el-table-column
							label="序号"
							type="index"
							width="80"
							align="center"
						/>
						<el-table-column
							label="标的编号"
							prop="bidItemCode"
							width="120"
							align="center"
						/>
						<el-table-column
							label="物料名称"
							prop="materialName"
							min-width="150"
						/>
						<el-table-column
							label="规格型号"
							prop="specification"
							min-width="120"
						/>
						<el-table-column
							label="需求数量"
							prop="quantity"
							width="100"
							align="center"
						/>
						<el-table-column
							label="计划价格"
							prop="plannedPrice"
							width="120"
							align="center"
						>
							<template #default="{ row }">
								<span class="price-text">{{ formatPrice(row.plannedPrice) }}</span>
							</template>
						</el-table-column>
						<el-table-column
							label="投标供应商"
							prop="suppliers"
							min-width="200"
						>
							<template #default="{ row }">
								<div class="suppliers-list">
									<div
										v-for="supplier in row.suppliers"
										:key="supplier.id"
										class="supplier-item"
										:class="{ 'winner': supplier.isWinner }"
									>
										<span class="supplier-name">{{ supplier.name }}</span>
										<span class="supplier-price">{{ formatPrice(supplier.bidPrice) }}</span>
										<el-tag
											v-if="supplier.isWinner"
											type="success"
											size="small"
										>
											中标
										</el-tag>
									</div>
								</div>
							</template>
						</el-table-column>
						<el-table-column
							label="可选择"
							width="100"
							align="center"
						>
							<template #default="{ row }">
								<el-link
									type="primary"
									@click="handleSelectWinner(row)"
								>
									选择
								</el-link>
							</template>
						</el-table-column>
						<el-table-column
							label="历史报价"
							width="100"
							align="center"
						>
							<template #default="{ row }">
								<el-link
									type="primary"
									@click="handleViewHistory(row)"
								>
									查看
								</el-link>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</div>
		</div>

		<!-- 选择中标供应商弹窗 -->
		<WinnerSelectionDialog
			v-model:visible="winnerDialogVisible"
			:bid-item="currentBidItem"
			@confirm="handleConfirmWinner"
		/>

		<!-- 历史报价弹窗 -->
		<HistoryPriceDialog
			v-model:visible="historyDialogVisible"
			:bid-item="currentBidItem"
		/>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import StageTabs from '../../bidding/StageTabs.vue';
import WinnerSelectionDialog from './WinnerSelectionDialog.vue';
import HistoryPriceDialog from './HistoryPriceDialog.vue';
import type { BidItem, Supplier } from './types';

// 标段数据
const tabs = [
	{ key: '1', label: '标段01: 物资采购物资采购...' },
	{ key: '2', label: '标段02: 物资采购物资' },
	{ key: '3', label: '标段03: 施工队伍安排' },
	{ key: '4', label: '标段04: 预算编制' },
];
const activeTabIndex = ref(0);

// 弹窗状态
const winnerDialogVisible = ref(false);
const historyDialogVisible = ref(false);
const currentBidItem = ref<BidItem | null>(null);

// 定标表格数据
const awardTableData = ref<BidItem[]>([
	{
		id: '1',
		bidItemCode: 'ASW12345',
		materialName: '特种合金01',
		specification: '规格合金01',
		quantity: 2000,
		plannedPrice: 241.22,
		suppliers: [
			{
				id: '1',
				name: '上海中华控股股份有限公司',
				bidPrice: 235.50,
				isWinner: true,
			},
			{
				id: '2',
				name: '定北汽车技术中心有限公司',
				bidPrice: 245.80,
				isWinner: false,
			},
			{
				id: '3',
				name: '来源华(中国)投资有限公司',
				bidPrice: 250.00,
				isWinner: false,
			},
		],
	},
	// 可以添加更多数据...
]);

// 格式化价格
function formatPrice(price: number): string {
	return `¥${price.toFixed(2)}`;
}

// 处理选择中标供应商
function handleSelectWinner(bidItem: BidItem) {
	currentBidItem.value = bidItem;
	winnerDialogVisible.value = true;
}

// 处理查看历史报价
function handleViewHistory(bidItem: BidItem) {
	currentBidItem.value = bidItem;
	historyDialogVisible.value = true;
}

// 确认中标供应商
function handleConfirmWinner(supplierId: string) {
	if (currentBidItem.value) {
		// 更新中标状态
		currentBidItem.value.suppliers.forEach(supplier => {
			supplier.isWinner = supplier.id === supplierId;
		});
	}
	winnerDialogVisible.value = false;
}

// 导出定标清单
function handleExport() {
	// 实现导出逻辑
	console.log('导出定标清单');
}

onMounted(() => {
	// 初始化数据
});
</script>

<style lang="scss" scoped>
.award-decision-container {
	padding: 20px;
	background: #fff;
	border-radius: 6px;
}

.stage-tabs-wrapper {
	margin-bottom: 20px;
}

.award-content {
	.award-info-section {
		.section-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16px;
			
			h3 {
				margin: 0;
				font-size: 16px;
				font-weight: 600;
				color: var(--Color-Text-text-color-primary, #1d2129);
			}
		}
	}
}

.award-table {
	.price-text {
		font-weight: 500;
		color: var(--Color-Primary-color-primary, #0069ff);
	}
	
	.suppliers-list {
		.supplier-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 4px 0;
			border-bottom: 1px solid #f0f0f0;
			
			&:last-child {
				border-bottom: none;
			}
			
			&.winner {
				background-color: #f6ffed;
				border-radius: 4px;
				padding: 4px 8px;
			}
			
			.supplier-name {
				flex: 1;
				font-size: 12px;
			}
			
			.supplier-price {
				margin: 0 8px;
				font-weight: 500;
				color: var(--Color-Primary-color-primary, #0069ff);
			}
		}
	}
}
</style>
