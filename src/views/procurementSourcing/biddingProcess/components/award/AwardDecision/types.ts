/**
 * 定标相关类型定义
 */

// 供应商信息
export interface Supplier {
  id: string;
  name: string;
  bidPrice: number;
  isWinner: boolean;
  contactPerson?: string;
  contactPhone?: string;
  email?: string;
  address?: string;
  creditCode?: string;
  qualificationLevel?: string;
  bidTime?: string;
  bidDocuments?: BidDocument[];
}

// 投标文档
export interface BidDocument {
  id: string;
  name: string;
  type: 'technical' | 'commercial' | 'qualification' | 'other';
  url: string;
  uploadTime: string;
  fileSize: number;
  status: 'pending' | 'approved' | 'rejected';
}

// 标的信息
export interface BidItem {
  id: string;
  bidItemCode: string;
  materialName: string;
  specification: string;
  quantity: number;
  unit?: string;
  plannedPrice: number;
  suppliers: Supplier[];
  category?: string;
  description?: string;
  technicalRequirements?: string[];
  deliveryRequirements?: {
    location: string;
    deadline: string;
    method: string;
  };
  qualityRequirements?: string[];
  awardCriteria?: 'lowest_price' | 'best_value' | 'technical_merit';
  awardStatus?: 'pending' | 'awarded' | 'cancelled';
  awardedSupplierId?: string;
  awardTime?: string;
  awardRemark?: string;
}

// 定标决策信息
export interface AwardDecision {
  id: string;
  projectId: string;
  bidItemId: string;
  winningSupplierId: string;
  awardPrice: number;
  awardTime: string;
  awardReason: string;
  evaluationScore?: number;
  evaluationComments?: string;
  approvalStatus: 'pending' | 'approved' | 'rejected';
  approvedBy?: string;
  approvedTime?: string;
  createdBy: string;
  createdTime: string;
  updatedTime: string;
}

// 定标公告信息
export interface AwardAnnouncement {
  id: string;
  projectId: string;
  title: string;
  content: string;
  publishTime: string;
  publishStatus: 'draft' | 'published' | 'cancelled';
  publishChannels: string[];
  attachments?: AnnouncementAttachment[];
  winnerList: WinnerInfo[];
  objectionPeriod: {
    startTime: string;
    endTime: string;
  };
  contactInfo: {
    person: string;
    phone: string;
    email: string;
    address: string;
  };
}

// 中标信息
export interface WinnerInfo {
  bidItemCode: string;
  materialName: string;
  winnerName: string;
  winnerPrice: number;
  winnerRank: number;
}

// 公告附件
export interface AnnouncementAttachment {
  id: string;
  name: string;
  url: string;
  type: string;
  size: number;
  uploadTime: string;
}

// 定标通知信息
export interface AwardNotification {
  id: string;
  projectId: string;
  supplierId: string;
  supplierName: string;
  notificationType: 'winner' | 'loser';
  title: string;
  content: string;
  sendTime: string;
  sendStatus: 'pending' | 'sent' | 'failed';
  sendMethod: 'email' | 'sms' | 'system' | 'all';
  readStatus?: 'unread' | 'read';
  readTime?: string;
  attachments?: NotificationAttachment[];
}

// 通知附件
export interface NotificationAttachment {
  id: string;
  name: string;
  url: string;
  type: string;
  size: number;
}

// 定标流程状态
export enum AwardStatus {
  PENDING = 'pending',           // 待定标
  IN_PROGRESS = 'in_progress',   // 定标中
  COMPLETED = 'completed',       // 已完成
  CANCELLED = 'cancelled',       // 已取消
  OBJECTION = 'objection',       // 异议期
  CONFIRMED = 'confirmed'        // 已确认
}

// 定标方式
export enum AwardMethod {
  LOWEST_PRICE = 'lowest_price',     // 最低价中标
  BEST_VALUE = 'best_value',         // 最佳性价比
  TECHNICAL_MERIT = 'technical_merit', // 技术优势
  COMPREHENSIVE = 'comprehensive'     // 综合评分
}

// 异议信息
export interface Objection {
  id: string;
  projectId: string;
  bidItemId: string;
  objectorId: string;
  objectorName: string;
  objectionType: 'procedure' | 'result' | 'qualification' | 'other';
  objectionContent: string;
  objectionTime: string;
  evidenceFiles?: ObjectionEvidence[];
  handleStatus: 'pending' | 'processing' | 'resolved' | 'rejected';
  handleResult?: string;
  handleTime?: string;
  handlerId?: string;
}

// 异议证据
export interface ObjectionEvidence {
  id: string;
  name: string;
  url: string;
  type: string;
  size: number;
  uploadTime: string;
}

// 定标统计信息
export interface AwardStatistics {
  totalBidItems: number;
  awardedItems: number;
  pendingItems: number;
  totalSuppliers: number;
  participatingSuppliers: number;
  winningSuppliers: number;
  averageDiscount: number;
  totalSavings: number;
  awardCompletionRate: number;
}

// 表格行数据类型（用于组件）
export type AwardTableRow = BidItem;

// 表单数据类型
export interface AwardFormData {
  bidItemId: string;
  winningSupplierId: string;
  awardReason: string;
  awardRemark?: string;
}

// API响应类型
export interface AwardApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  success: boolean;
}

// 分页数据类型
export interface PaginatedData<T> {
  list: T[];
  total: number;
  pageNum: number;
  pageSize: number;
  pages: number;
}
