<template>
	<div class="award-management-container">
		<!-- 步骤指示器 -->
		<StepsIndicator
			:steps="stepsData"
			@step-click="handleStepClick"
		/>

		<!-- 动态组件渲染区域 -->
		<div class="component-content">
			<component :is="currentComponent" />
		</div>

		<!-- 底部操作按钮 -->
		<div class="form-actions-wrapper">
			<div class="form-actions">
				<el-button
					type="primary"
					@click="handleSave"
					:loading="saving"
				>
					保存
				</el-button>
				<el-button @click="handleCancel">取消</el-button>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import StepsIndicator, { type StepItem } from '../../StepsIndicator/index.vue';
import AwardResult from '../AwardResult/index.vue';
import WinnerPublicity from '../WinnerPublicity/index.vue';
import WinnerAnnouncement from '../WinnerAnnouncement/index.vue';
import WinnerNotification from '../WinnerNotification/index.vue';

// 当前激活的步骤索引
const activeStepIndex = ref(0);

// 步骤数据
const stepsData = ref<StepItem[]>([
	{
		id: 0,
		number: 1,
		label: '定标结果',
		completed: false,
		current: true,
	},
	{
		id: 1,
		number: 2,
		label: '中标公示',
		completed: false,
		current: false,
	},
	{
		id: 2,
		number: 3,
		label: '中标公告',
		completed: false,
		current: false,
	},
	{
		id: 3,
		number: 4,
		label: '中标通知书',
		completed: false,
		current: false,
	},
]);

// 组件映射
const componentMap = {
	0: AwardResult,
	1: WinnerPublicity,
	2: WinnerAnnouncement,
	3: WinnerNotification,
};

// 当前显示的组件
const currentComponent = computed(() => {
	return componentMap[activeStepIndex.value as keyof typeof componentMap];
});

// 处理步骤点击事件
function handleStepClick(step: StepItem, index: number) {
	activeStepIndex.value = index;
}
</script>

<style lang="scss" scoped>
.award-management-container {
	min-height: 100%;
	display: flex;
	flex-direction: column;
	.component-content {
		flex: 1;
		margin-top: 12px;
		border-radius: 6px;
		//background: var(--Color-Fill-fill-color-blank, #fff);
	}
}
</style>
