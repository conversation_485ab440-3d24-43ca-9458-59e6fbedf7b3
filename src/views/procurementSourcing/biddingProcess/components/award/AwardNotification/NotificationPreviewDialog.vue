<template>
	<el-dialog
		v-model="visible"
		:title="`通知预览 - ${notificationData?.supplierName}`"
		width="700px"
		:before-close="handleClose"
	>
		<div class="notification-preview">
			<div class="notification-header">
				<h3 class="notification-title">{{ notificationData?.title }}</h3>
				<div class="notification-meta">
					<el-tag
						:type="notificationData?.notificationType === 'winner' ? 'success' : 'info'"
						size="small"
					>
						{{ notificationData?.notificationType === 'winner' ? '中标通知' : '未中标通知' }}
					</el-tag>
				</div>
			</div>

			<div class="notification-body">
				<!-- 收件人信息 -->
				<div class="recipient-info">
					<h4>收件人信息</h4>
					<el-descriptions :column="2" border>
						<el-descriptions-item label="供应商名称">{{ notificationData?.supplierName }}</el-descriptions-item>
						<el-descriptions-item label="发送方式">{{ getSendMethodText(notificationData?.sendMethod) }}</el-descriptions-item>
						<el-descriptions-item label="发送状态">
							<el-tag
								:type="getStatusTagType(notificationData?.sendStatus)"
								size="small"
							>
								{{ getStatusText(notificationData?.sendStatus) }}
							</el-tag>
						</el-descriptions-item>
						<el-descriptions-item label="发送时间">{{ notificationData?.sendTime || '未发送' }}</el-descriptions-item>
					</el-descriptions>
				</div>

				<!-- 通知内容 -->
				<div class="notification-content">
					<h4>通知内容</h4>
					<div class="content-body">
						<div class="formal-header">
							<p><strong>{{ notificationData?.supplierName }}：</strong></p>
						</div>

						<div class="main-content">
							<p v-if="notificationData?.notificationType === 'winner'">
								恭喜您在"XDMY-LYMG-20240810李原牧歌牧场原料采购项目"中成功中标！
							</p>
							<p v-else>
								感谢您参与"XDMY-LYMG-20240810李原牧歌牧场原料采购项目"的投标。
							</p>

							<div v-if="notificationData?.notificationType === 'winner'" class="winner-details">
								<h5>中标详情：</h5>
								<ul>
									<li>项目名称：XDMY-LYMG-20240810李原牧歌牧场原料采购项目</li>
									<li>项目编号：XDMY-LYMG-20240810</li>
									<li>中标标的：特种合金01</li>
									<li>中标金额：¥235.50</li>
									<li>定标时间：{{ formatDate(new Date()) }}</li>
								</ul>

								<h5>后续事项：</h5>
								<ol>
									<li>请于收到本通知后3个工作日内与采购人联系，确认中标事宜；</li>
									<li>请按照招标文件要求，在规定时间内签订采购合同；</li>
									<li>如有疑问，请及时与我们联系。</li>
								</ol>
							</div>

							<div v-else class="loser-details">
								<p>经过公开、公平、公正的评标程序，您的投标未能中标。</p>
								<p>感谢您对本项目的关注和参与，期待与您在今后的项目中继续合作。</p>
								
								<h5>如有异议：</h5>
								<p>如对本次定标结果有异议，可在公告发布之日起7个工作日内，以书面形式向采购人提出质疑。</p>
							</div>
						</div>

						<div class="contact-info">
							<h5>联系方式：</h5>
							<p>联系人：王小二</p>
							<p>联系电话：010-12345678</p>
							<p>邮箱：<EMAIL></p>
							<p>地址：北京市朝阳区某某街道123号</p>
						</div>

						<div class="formal-footer">
							<p>此致</p>
							<p>敬礼！</p>
							<br>
							<p style="text-align: right;">
								<strong>采购人：XDMY-LYMG-20240810李原牧歌牧场</strong><br>
								<strong>{{ formatDate(new Date()) }}</strong>
							</p>
						</div>
					</div>
				</div>

				<!-- 附件信息 -->
				<div v-if="notificationData?.attachments?.length" class="attachments-info">
					<h4>附件信息</h4>
					<el-table
						:data="notificationData.attachments"
						style="width: 100%"
						size="small"
					>
						<el-table-column label="文件名" prop="name" />
						<el-table-column label="文件大小" prop="size" width="100" />
						<el-table-column label="操作" width="80">
							<template #default="{ row }">
								<el-link type="primary" :href="row.url" target="_blank">下载</el-link>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</div>
		</div>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="handleClose">关闭</el-button>
				<el-button type="primary" @click="handleEdit">编辑</el-button>
				<el-button
					v-if="notificationData?.sendStatus === 'pending'"
					type="success"
					@click="handleSend"
				>
					发送
				</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import type { AwardNotification } from '../AwardDecision/types';

interface Props {
	visible: boolean;
	notificationData: AwardNotification | null;
}

interface Emits {
	(e: 'update:visible', value: boolean): void;
	(e: 'edit', notification: AwardNotification): void;
	(e: 'send', notification: AwardNotification): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(props.visible);

// 监听visible变化
watch(() => props.visible, (newVal) => {
	visible.value = newVal;
});

watch(visible, (newVal) => {
	emit('update:visible', newVal);
});

// 获取发送方式文本
function getSendMethodText(method?: string): string {
	switch (method) {
		case 'email':
			return '邮件';
		case 'sms':
			return '短信';
		case 'system':
			return '系统通知';
		case 'all':
			return '全部方式';
		default:
			return '未设置';
	}
}

// 获取状态标签类型
function getStatusTagType(status?: string): string {
	switch (status) {
		case 'pending':
			return 'info';
		case 'sent':
			return 'success';
		case 'failed':
			return 'danger';
		default:
			return '';
	}
}

// 获取状态文本
function getStatusText(status?: string): string {
	switch (status) {
		case 'pending':
			return '待发送';
		case 'sent':
			return '已发送';
		case 'failed':
			return '发送失败';
		default:
			return '未知';
	}
}

// 格式化日期
function formatDate(date: Date): string {
	return date.toLocaleDateString('zh-CN', {
		year: 'numeric',
		month: '2-digit',
		day: '2-digit',
	});
}

// 处理关闭
function handleClose() {
	visible.value = false;
}

// 处理编辑
function handleEdit() {
	if (props.notificationData) {
		emit('edit', props.notificationData);
	}
	handleClose();
}

// 处理发送
function handleSend() {
	if (props.notificationData) {
		emit('send', props.notificationData);
	}
	handleClose();
}
</script>

<style lang="scss" scoped>
.notification-preview {
	.notification-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20px;
		padding-bottom: 15px;
		border-bottom: 1px solid #e5e7eb;

		.notification-title {
			margin: 0;
			font-size: 18px;
			font-weight: 600;
			color: #1d2129;
		}
	}

	.notification-body {
		.recipient-info,
		.notification-content,
		.attachments-info {
			margin-bottom: 24px;

			h4 {
				margin: 0 0 12px 0;
				font-size: 14px;
				font-weight: 600;
				color: #1d2129;
			}
		}

		.notification-content {
			.content-body {
				background: #f9fafb;
				padding: 20px;
				border-radius: 6px;
				border: 1px solid #e5e7eb;
				font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
				line-height: 1.6;

				.formal-header,
				.main-content,
				.contact-info,
				.formal-footer {
					margin-bottom: 20px;

					&:last-child {
						margin-bottom: 0;
					}
				}

				.winner-details,
				.loser-details {
					margin: 15px 0;

					h5 {
						margin: 15px 0 8px 0;
						font-size: 14px;
						font-weight: 600;
						color: #1d2129;
					}

					ul,
					ol {
						margin: 8px 0;
						padding-left: 20px;

						li {
							margin: 5px 0;
						}
					}
				}

				p {
					margin: 8px 0;
					font-size: 14px;
				}
			}
		}
	}
}

.dialog-footer {
	text-align: right;
}
</style>
