<template>
	<el-dialog
		v-model="visible"
		title="批量发送确认"
		width="600px"
		:before-close="handleClose"
	>
		<div class="send-confirm-content">
			<div class="confirm-info">
				<el-alert
					title="确认发送"
					:description="`即将向 ${notificationList.length} 个供应商发送定标通知，请确认发送信息。`"
					type="warning"
					:closable="false"
					show-icon
				/>
			</div>

			<div class="notification-list">
				<h4>发送列表</h4>
				<el-table
					:data="notificationList"
					style="width: 100%"
					max-height="300"
					border
					size="small"
				>
					<el-table-column
						label="序号"
						type="index"
						width="60"
						align="center"
					/>
					<el-table-column
						label="供应商名称"
						prop="supplierName"
						min-width="200"
					/>
					<el-table-column
						label="通知类型"
						prop="notificationType"
						width="100"
						align="center"
					>
						<template #default="{ row }">
							<el-tag
								:type="row.notificationType === 'winner' ? 'success' : 'info'"
								size="small"
							>
								{{ row.notificationType === 'winner' ? '中标' : '未中标' }}
							</el-tag>
						</template>
					</el-table-column>
					<el-table-column
						label="发送方式"
						prop="sendMethod"
						width="100"
						align="center"
					>
						<template #default="{ row }">
							<el-select
								v-model="row.sendMethod"
								size="small"
								style="width: 80px"
							>
								<el-option label="邮件" value="email" />
								<el-option label="短信" value="sms" />
								<el-option label="系统" value="system" />
								<el-option label="全部" value="all" />
							</el-select>
						</template>
					</el-table-column>
				</el-table>
			</div>

			<div class="send-options">
				<h4>发送选项</h4>
				<el-form label-width="120px">
					<el-form-item label="发送时间">
						<el-radio-group v-model="sendTimeOption">
							<el-radio label="immediate">立即发送</el-radio>
							<el-radio label="scheduled">定时发送</el-radio>
						</el-radio-group>
					</el-form-item>

					<el-form-item
						v-if="sendTimeOption === 'scheduled'"
						label="发送时间"
					>
						<el-date-picker
							v-model="scheduledTime"
							type="datetime"
							placeholder="选择发送时间"
							style="width: 200px"
							:disabled-date="disabledDate"
						/>
					</el-form-item>

					<el-form-item label="发送确认">
						<el-checkbox v-model="requireConfirmation">
							发送前再次确认
						</el-checkbox>
					</el-form-item>

					<el-form-item label="失败重试">
						<el-checkbox v-model="autoRetry">
							发送失败时自动重试
						</el-checkbox>
					</el-form-item>
				</el-form>
			</div>

			<div class="send-summary">
				<h4>发送统计</h4>
				<el-descriptions :column="2" border size="small">
					<el-descriptions-item label="总数量">{{ notificationList.length }}</el-descriptions-item>
					<el-descriptions-item label="中标通知">{{ winnerCount }}</el-descriptions-item>
					<el-descriptions-item label="未中标通知">{{ loserCount }}</el-descriptions-item>
					<el-descriptions-item label="预计发送时间">{{ estimatedTime }}</el-descriptions-item>
				</el-descriptions>
			</div>
		</div>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="handleClose">取消</el-button>
				<el-button
					type="primary"
					:loading="sending"
					@click="handleConfirm"
				>
					{{ sendTimeOption === 'immediate' ? '立即发送' : '设置定时发送' }}
				</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import type { AwardNotification } from '../AwardDecision/types';

interface Props {
	visible: boolean;
	notificationList: AwardNotification[];
}

interface Emits {
	(e: 'update:visible', value: boolean): void;
	(e: 'confirm'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(props.visible);
const sending = ref(false);

// 发送选项
const sendTimeOption = ref<'immediate' | 'scheduled'>('immediate');
const scheduledTime = ref<Date>();
const requireConfirmation = ref(false);
const autoRetry = ref(true);

// 监听visible变化
watch(() => props.visible, (newVal) => {
	visible.value = newVal;
	if (newVal) {
		// 重置表单
		sendTimeOption.value = 'immediate';
		scheduledTime.value = undefined;
		requireConfirmation.value = false;
		autoRetry.value = true;
	}
});

watch(visible, (newVal) => {
	emit('update:visible', newVal);
});

// 计算属性
const winnerCount = computed(() => {
	return props.notificationList.filter(n => n.notificationType === 'winner').length;
});

const loserCount = computed(() => {
	return props.notificationList.filter(n => n.notificationType === 'loser').length;
});

const estimatedTime = computed(() => {
	if (sendTimeOption.value === 'immediate') {
		return '约1-2分钟';
	} else if (scheduledTime.value) {
		return scheduledTime.value.toLocaleString('zh-CN');
	} else {
		return '请选择发送时间';
	}
});

// 禁用过去的日期
function disabledDate(time: Date): boolean {
	return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
}

// 处理关闭
function handleClose() {
	visible.value = false;
}

// 处理确认
async function handleConfirm() {
	// 验证定时发送时间
	if (sendTimeOption.value === 'scheduled' && !scheduledTime.value) {
		ElMessage.warning('请选择发送时间');
		return;
	}

	// 二次确认
	if (requireConfirmation.value) {
		try {
			await ElMessageBox.confirm(
				`确定要发送 ${props.notificationList.length} 条通知吗？`,
				'最终确认',
				{
					confirmButtonText: '确定发送',
					cancelButtonText: '取消',
					type: 'warning',
				}
			);
		} catch {
			return;
		}
	}

	sending.value = true;

	try {
		// 模拟发送过程
		await new Promise(resolve => setTimeout(resolve, 2000));

		if (sendTimeOption.value === 'immediate') {
			ElMessage.success('通知发送成功');
		} else {
			ElMessage.success('定时发送任务已设置');
		}

		emit('confirm');
	} catch (error) {
		ElMessage.error('发送失败，请重试');
	} finally {
		sending.value = false;
	}
}
</script>

<style lang="scss" scoped>
.send-confirm-content {
	.confirm-info {
		margin-bottom: 20px;
	}

	.notification-list,
	.send-options,
	.send-summary {
		margin-bottom: 20px;

		h4 {
			margin: 0 0 12px 0;
			font-size: 14px;
			font-weight: 600;
			color: #1d2129;
		}
	}

	.send-options {
		background: #f9fafb;
		padding: 16px;
		border-radius: 6px;
		border: 1px solid #e5e7eb;
	}
}

.dialog-footer {
	text-align: right;
}
</style>
