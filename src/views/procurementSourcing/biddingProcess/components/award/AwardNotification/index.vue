<template>
	<div class="award-notification-container">
		<div class="notification-content">
			<!-- 通知管理 -->
			<div class="notification-section">
				<div class="section-header">
					<h3>定标通知</h3>
					<div class="header-actions">
						<el-button
							type="primary"
							size="small"
							@click="handleBatchSend"
						>
							批量发送
						</el-button>
						<el-button
							type="success"
							size="small"
							@click="handleSendAll"
						>
							发送全部
						</el-button>
					</div>
				</div>

				<!-- 通知列表 -->
				<div class="notification-table">
					<el-table
						ref="tableRef"
						:data="notificationList"
						style="width: 100%"
						border
						stripe
						@selection-change="handleSelectionChange"
					>
						<el-table-column
							type="selection"
							width="55"
							align="center"
						/>
						<el-table-column
							label="序号"
							type="index"
							width="60"
							align="center"
						/>
						<el-table-column
							label="供应商名称"
							prop="supplierName"
							min-width="200"
						/>
						<el-table-column
							label="通知类型"
							prop="notificationType"
							width="100"
							align="center"
						>
							<template #default="{ row }">
								<el-tag
									:type="row.notificationType === 'winner' ? 'success' : 'info'"
									size="small"
								>
									{{ row.notificationType === 'winner' ? '中标通知' : '未中标通知' }}
								</el-tag>
							</template>
						</el-table-column>
						<el-table-column
							label="发送方式"
							prop="sendMethod"
							width="120"
							align="center"
						>
							<template #default="{ row }">
								<el-select
									v-model="row.sendMethod"
									size="small"
									style="width: 100px"
								>
									<el-option label="邮件" value="email" />
									<el-option label="短信" value="sms" />
									<el-option label="系统" value="system" />
									<el-option label="全部" value="all" />
								</el-select>
							</template>
						</el-table-column>
						<el-table-column
							label="发送状态"
							prop="sendStatus"
							width="100"
							align="center"
						>
							<template #default="{ row }">
								<el-tag
									:type="getStatusTagType(row.sendStatus)"
									size="small"
								>
									{{ getStatusText(row.sendStatus) }}
								</el-tag>
							</template>
						</el-table-column>
						<el-table-column
							label="发送时间"
							prop="sendTime"
							width="150"
							align="center"
						>
							<template #default="{ row }">
								<span>{{ row.sendTime || '-' }}</span>
							</template>
						</el-table-column>
						<el-table-column
							label="阅读状态"
							prop="readStatus"
							width="100"
							align="center"
						>
							<template #default="{ row }">
								<el-tag
									v-if="row.sendStatus === 'sent'"
									:type="row.readStatus === 'read' ? 'success' : 'warning'"
									size="small"
								>
									{{ row.readStatus === 'read' ? '已读' : '未读' }}
								</el-tag>
								<span v-else class="text-gray-500">-</span>
							</template>
						</el-table-column>
						<el-table-column
							label="操作"
							width="200"
							align="center"
							fixed="right"
						>
							<template #default="{ row }">
								<el-button
									type="primary"
									size="small"
									@click="handlePreview(row)"
								>
									预览
								</el-button>
								<el-button
									v-if="row.sendStatus === 'pending'"
									type="success"
									size="small"
									@click="handleSend(row)"
								>
									发送
								</el-button>
								<el-button
									v-else
									type="info"
									size="small"
									@click="handleResend(row)"
								>
									重发
								</el-button>
							</template>
						</el-table-column>
					</el-table>
				</div>
			</div>
		</div>

		<!-- 通知预览弹窗 -->
		<NotificationPreviewDialog
			v-model:visible="previewDialogVisible"
			:notification-data="currentNotification"
		/>

		<!-- 发送确认弹窗 -->
		<SendConfirmDialog
			v-model:visible="sendConfirmDialogVisible"
			:notification-list="selectedNotifications"
			@confirm="handleConfirmSend"
		/>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import NotificationPreviewDialog from './NotificationPreviewDialog.vue';
import SendConfirmDialog from './SendConfirmDialog.vue';
import type { AwardNotification } from '../AwardDecision/types';

// 表格引用
const tableRef = ref();

// 弹窗状态
const previewDialogVisible = ref(false);
const sendConfirmDialogVisible = ref(false);

// 当前通知数据
const currentNotification = ref<AwardNotification | null>(null);

// 选中的通知列表
const selectedNotifications = ref<AwardNotification[]>([]);

// 通知列表数据
const notificationList = ref<AwardNotification[]>([
	{
		id: '1',
		projectId: 'XDMY-LYMG-20240810',
		supplierId: '1',
		supplierName: '上海中华控股股份有限公司',
		notificationType: 'winner',
		title: '中标通知书',
		content: '恭喜您在XDMY-LYMG-20240810李原牧歌牧场原料采购项目中成功中标...',
		sendTime: '',
		sendStatus: 'pending',
		sendMethod: 'email',
		readStatus: 'unread',
	},
	{
		id: '2',
		projectId: 'XDMY-LYMG-20240810',
		supplierId: '2',
		supplierName: '定北汽车技术中心有限公司',
		notificationType: 'loser',
		title: '未中标通知书',
		content: '感谢您参与XDMY-LYMG-20240810李原牧歌牧场原料采购项目投标...',
		sendTime: '',
		sendStatus: 'pending',
		sendMethod: 'email',
		readStatus: 'unread',
	},
	{
		id: '3',
		projectId: 'XDMY-LYMG-20240810',
		supplierId: '3',
		supplierName: '来源华(中国)投资有限公司',
		notificationType: 'loser',
		title: '未中标通知书',
		content: '感谢您参与XDMY-LYMG-20240810李原牧歌牧场原料采购项目投标...',
		sendTime: '',
		sendStatus: 'pending',
		sendMethod: 'email',
		readStatus: 'unread',
	},
]);

// 获取状态标签类型
function getStatusTagType(status: string): string {
	switch (status) {
		case 'pending':
			return 'info';
		case 'sent':
			return 'success';
		case 'failed':
			return 'danger';
		default:
			return '';
	}
}

// 获取状态文本
function getStatusText(status: string): string {
	switch (status) {
		case 'pending':
			return '待发送';
		case 'sent':
			return '已发送';
		case 'failed':
			return '发送失败';
		default:
			return '未知';
	}
}

// 处理选择变化
function handleSelectionChange(selection: AwardNotification[]) {
	selectedNotifications.value = selection;
}

// 处理预览
function handlePreview(notification: AwardNotification) {
	currentNotification.value = notification;
	previewDialogVisible.value = true;
}

// 处理单个发送
async function handleSend(notification: AwardNotification) {
	try {
		await ElMessageBox.confirm(
			`确定要发送通知给 ${notification.supplierName} 吗？`,
			'确认发送',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}
		);

		// 模拟发送
		notification.sendStatus = 'sent';
		notification.sendTime = new Date().toLocaleString();
		
		ElMessage.success('通知发送成功');
	} catch {
		// 用户取消
	}
}

// 处理重发
async function handleResend(notification: AwardNotification) {
	try {
		await ElMessageBox.confirm(
			`确定要重新发送通知给 ${notification.supplierName} 吗？`,
			'确认重发',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}
		);

		// 模拟重发
		notification.sendStatus = 'sent';
		notification.sendTime = new Date().toLocaleString();
		
		ElMessage.success('通知重发成功');
	} catch {
		// 用户取消
	}
}

// 处理批量发送
function handleBatchSend() {
	if (selectedNotifications.value.length === 0) {
		ElMessage.warning('请先选择要发送的通知');
		return;
	}
	sendConfirmDialogVisible.value = true;
}

// 处理发送全部
function handleSendAll() {
	const pendingNotifications = notificationList.value.filter(n => n.sendStatus === 'pending');
	if (pendingNotifications.length === 0) {
		ElMessage.warning('没有待发送的通知');
		return;
	}
	selectedNotifications.value = pendingNotifications;
	sendConfirmDialogVisible.value = true;
}

// 确认发送
function handleConfirmSend() {
	selectedNotifications.value.forEach(notification => {
		notification.sendStatus = 'sent';
		notification.sendTime = new Date().toLocaleString();
	});
	
	ElMessage.success(`成功发送 ${selectedNotifications.value.length} 条通知`);
	sendConfirmDialogVisible.value = false;
	
	// 清空选择
	tableRef.value?.clearSelection();
}

onMounted(() => {
	// 初始化数据
});
</script>

<style lang="scss" scoped>
.award-notification-container {
	padding: 20px;
	background: #fff;
	border-radius: 6px;
}

.notification-content {
	.notification-section {
		.section-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16px;
			
			h3 {
				margin: 0;
				font-size: 16px;
				font-weight: 600;
				color: var(--Color-Text-text-color-primary, #1d2129);
			}
			
			.header-actions {
				display: flex;
				gap: 8px;
			}
		}
	}
}

.text-gray-500 {
	color: #6b7280;
}
</style>
