<template>
	<el-dialog
		v-model="visible"
		title="上传文档"
		width="500px"
		:before-close="handleClose"
	>
		<div class="upload-content">
			<el-form
				ref="formRef"
				:model="uploadForm"
				:rules="formRules"
				label-width="100px"
			>
				<el-form-item label="文档类型" prop="type">
					<el-select
						v-model="uploadForm.type"
						placeholder="请选择文档类型"
						style="width: 100%"
					>
						<el-option label="定标决定书" value="decision" />
						<el-option label="评标报告" value="report" />
						<el-option label="定标通知" value="notice" />
						<el-option label="合同文件" value="contract" />
						<el-option label="其他文档" value="other" />
					</el-select>
				</el-form-item>

				<el-form-item label="文档名称" prop="name">
					<el-input
						v-model="uploadForm.name"
						placeholder="请输入文档名称"
						maxlength="100"
						show-word-limit
					/>
				</el-form-item>

				<el-form-item label="文档描述">
					<el-input
						v-model="uploadForm.description"
						type="textarea"
						:rows="3"
						placeholder="请输入文档描述（选填）"
						maxlength="200"
						show-word-limit
					/>
				</el-form-item>

				<el-form-item label="选择文件" prop="file">
					<el-upload
						ref="uploadRef"
						class="upload-demo"
						:auto-upload="false"
						:on-change="handleFileChange"
						:on-remove="handleFileRemove"
						:file-list="fileList"
						:limit="1"
						accept=".pdf,.doc,.docx,.xls,.xlsx,.txt"
					>
						<el-button type="primary">选择文件</el-button>
						<template #tip>
							<div class="el-upload__tip">
								支持 PDF、Word、Excel、TXT 格式，文件大小不超过 10MB
							</div>
						</template>
					</el-upload>
				</el-form-item>
			</el-form>
		</div>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="handleClose">取消</el-button>
				<el-button
					type="primary"
					:loading="uploading"
					:disabled="!uploadForm.file"
					@click="handleUpload"
				>
					上传
				</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import type { UploadFile, UploadFiles } from 'element-plus';

interface Props {
	visible: boolean;
}

interface Emits {
	(e: 'update:visible', value: boolean): void;
	(e: 'upload-success', file: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(props.visible);
const uploading = ref(false);

// 表单引用
const formRef = ref();
const uploadRef = ref();

// 上传表单
const uploadForm = reactive({
	type: '',
	name: '',
	description: '',
	file: null as File | null,
});

// 文件列表
const fileList = ref<UploadFiles>([]);

// 表单验证规则
const formRules = {
	type: [
		{ required: true, message: '请选择文档类型', trigger: 'change' },
	],
	name: [
		{ required: true, message: '请输入文档名称', trigger: 'blur' },
		{ max: 100, message: '文档名称不能超过100个字符', trigger: 'blur' },
	],
	file: [
		{ required: true, message: '请选择要上传的文件', trigger: 'change' },
	],
};

// 监听visible变化
watch(() => props.visible, (newVal) => {
	visible.value = newVal;
	if (newVal) {
		// 重置表单
		resetForm();
	}
});

watch(visible, (newVal) => {
	emit('update:visible', newVal);
});

// 重置表单
function resetForm() {
	uploadForm.type = '';
	uploadForm.name = '';
	uploadForm.description = '';
	uploadForm.file = null;
	fileList.value = [];
	formRef.value?.clearValidate();
}

// 处理文件变化
function handleFileChange(file: UploadFile) {
	// 验证文件大小
	if (file.size && file.size > 10 * 1024 * 1024) {
		ElMessage.error('文件大小不能超过 10MB');
		return false;
	}

	// 验证文件类型
	const allowedTypes = [
		'application/pdf',
		'application/msword',
		'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
		'application/vnd.ms-excel',
		'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
		'text/plain',
	];

	if (file.raw && !allowedTypes.includes(file.raw.type)) {
		ElMessage.error('不支持的文件格式');
		return false;
	}

	uploadForm.file = file.raw || null;

	// 自动填充文档名称
	if (file.name && !uploadForm.name) {
		uploadForm.name = file.name.replace(/\.[^/.]+$/, ''); // 移除文件扩展名
	}

	return true;
}

// 处理文件移除
function handleFileRemove() {
	uploadForm.file = null;
}

// 处理关闭
function handleClose() {
	visible.value = false;
}

// 处理上传
async function handleUpload() {
	try {
		await formRef.value?.validate();

		uploading.value = true;

		// 模拟上传过程
		await new Promise(resolve => setTimeout(resolve, 2000));

		// 构造上传成功的文件信息
		const uploadedFile = {
			name: uploadForm.name,
			type: uploadForm.type,
			description: uploadForm.description,
			size: uploadForm.file?.size || 0,
			url: `/documents/${uploadForm.file?.name}`, // 模拟URL
			originalName: uploadForm.file?.name,
		};

		emit('upload-success', uploadedFile);
		handleClose();
	} catch (error) {
		console.error('表单验证失败:', error);
	} finally {
		uploading.value = false;
	}
}
</script>

<style lang="scss" scoped>
.upload-content {
	.upload-demo {
		width: 100%;
	}
}

.dialog-footer {
	text-align: right;
}

:deep(.el-upload__tip) {
	margin-top: 8px;
	font-size: 12px;
	color: #6b7280;
}
</style>
