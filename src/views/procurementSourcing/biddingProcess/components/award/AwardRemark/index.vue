<template>
	<div class="award-remark-container">
		<div class="remark-content">
			<!-- 定标备注 -->
			<div class="remark-section">
				<div class="section-header">
					<h3>定标备注</h3>
					<div class="header-actions">
						<el-button
							type="primary"
							size="small"
							@click="handleSave"
							:loading="saving"
						>
							保存
						</el-button>
					</div>
				</div>

				<el-form
					ref="formRef"
					:model="remarkForm"
					label-width="120px"
				>
					<el-form-item label="定标人">
						<el-input
							v-model="remarkForm.awardPerson"
							placeholder="请输入定标人姓名"
							readonly
						/>
					</el-form-item>

					<el-form-item label="定标时间">
						<el-date-picker
							v-model="remarkForm.awardTime"
							type="datetime"
							placeholder="选择定标时间"
							style="width: 100%"
							readonly
						/>
					</el-form-item>

					<el-form-item label="定标说明">
						<el-input
							v-model="remarkForm.awardDescription"
							type="textarea"
							:rows="6"
							placeholder="请输入定标说明"
							maxlength="1000"
							show-word-limit
						/>
					</el-form-item>

					<el-form-item label="备注信息">
						<el-input
							v-model="remarkForm.remark"
							type="textarea"
							:rows="4"
							placeholder="请输入备注信息（选填）"
							maxlength="500"
							show-word-limit
						/>
					</el-form-item>
				</el-form>
			</div>

			<!-- 定标历史记录 -->
			<div class="history-section">
				<div class="section-header">
					<h3>定标历史记录</h3>
				</div>

				<el-timeline>
					<el-timeline-item
						v-for="(record, index) in historyRecords"
						:key="index"
						:timestamp="record.timestamp"
						:type="record.type"
						:icon="record.icon"
					>
						<div class="timeline-content">
							<h4>{{ record.title }}</h4>
							<p>{{ record.description }}</p>
							<div v-if="record.details" class="record-details">
								<el-descriptions :column="2" size="small">
									<el-descriptions-item
										v-for="(value, key) in record.details"
										:key="key"
										:label="key"
									>
										{{ value }}
									</el-descriptions-item>
								</el-descriptions>
							</div>
						</div>
					</el-timeline-item>
				</el-timeline>
			</div>

			<!-- 相关文档 -->
			<div class="documents-section">
				<div class="section-header">
					<h3>相关文档</h3>
					<div class="header-actions">
						<el-button
							type="primary"
							size="small"
							@click="handleUpload"
						>
							上传文档
						</el-button>
					</div>
				</div>

				<el-table
					:data="documentList"
					style="width: 100%"
					border
					stripe
				>
					<el-table-column
						label="序号"
						type="index"
						width="60"
						align="center"
					/>
					<el-table-column
						label="文档名称"
						prop="name"
						min-width="200"
					/>
					<el-table-column
						label="文档类型"
						prop="type"
						width="120"
						align="center"
					>
						<template #default="{ row }">
							<el-tag size="small">{{ getDocumentTypeText(row.type) }}</el-tag>
						</template>
					</el-table-column>
					<el-table-column
						label="文件大小"
						prop="size"
						width="100"
						align="center"
					>
						<template #default="{ row }">
							{{ formatFileSize(row.size) }}
						</template>
					</el-table-column>
					<el-table-column
						label="上传时间"
						prop="uploadTime"
						width="150"
						align="center"
					/>
					<el-table-column
						label="操作"
						width="150"
						align="center"
						fixed="right"
					>
						<template #default="{ row }">
							<el-button
								type="primary"
								size="small"
								@click="handleDownload(row)"
							>
								下载
							</el-button>
							<el-button
								type="danger"
								size="small"
								@click="handleDelete(row)"
							>
								删除
							</el-button>
						</template>
					</el-table-column>
				</el-table>
			</div>
		</div>

		<!-- 文档上传弹窗 -->
		<DocumentUploadDialog
			v-model:visible="uploadDialogVisible"
			@upload-success="handleUploadSuccess"
		/>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { SuccessFilled, Warning, InfoFilled } from '@element-plus/icons-vue';
import DocumentUploadDialog from './DocumentUploadDialog.vue';

// 表单引用
const formRef = ref();

// 保存状态
const saving = ref(false);

// 上传弹窗状态
const uploadDialogVisible = ref(false);

// 定标备注表单
const remarkForm = reactive({
	awardPerson: '王小二',
	awardTime: new Date('2025-01-31 12:22:00'),
	awardDescription: `经过公开、公平、公正的评标程序，根据评标委员会的评审意见，现确定以下定标结果：

1. 标的编号ASW12345（特种合金01）：上海中华控股股份有限公司中标，中标价格¥235.50。

2. 定标依据：
   - 符合招标文件的技术要求
   - 报价合理，性价比最优
   - 企业资质齐全，信誉良好
   - 供货能力满足项目需求

3. 定标原则：
   - 严格按照招标文件规定的评标标准
   - 综合考虑技术、商务、服务等因素
   - 确保采购质量和效益

本次定标结果已通过内部审批程序，符合相关法律法规要求。`,
	remark: '定标过程规范，结果公正合理。',
});

// 历史记录数据
const historyRecords = ref([
	{
		timestamp: '2025-01-31 12:22',
		title: '定标完成',
		description: '项目定标工作已完成，确定中标供应商',
		type: 'success',
		icon: SuccessFilled,
		details: {
			'定标人': '王小二',
			'中标供应商': '上海中华控股股份有限公司',
			'中标金额': '¥235.50',
		},
	},
	{
		timestamp: '2025-01-30 16:30',
		title: '评标结束',
		description: '评标委员会完成评标工作，提交评标报告',
		type: 'primary',
		icon: InfoFilled,
		details: {
			'评标委员会': '5人',
			'评标时长': '4小时',
			'推荐中标候选人': '上海中华控股股份有限公司',
		},
	},
	{
		timestamp: '2025-01-30 09:00',
		title: '开标完成',
		description: '开标工作顺利完成，3家供应商参与投标',
		type: 'warning',
		icon: Warning,
		details: {
			'参与供应商': '3家',
			'有效投标': '3份',
			'废标': '0份',
		},
	},
]);

// 文档列表数据
const documentList = ref([
	{
		id: '1',
		name: '定标决定书.pdf',
		type: 'decision',
		size: 1024 * 1024 * 2.5, // 2.5MB
		uploadTime: '2025-01-31 12:30',
		url: '/documents/award-decision.pdf',
	},
	{
		id: '2',
		name: '评标报告.pdf',
		type: 'report',
		size: 1024 * 1024 * 5.2, // 5.2MB
		uploadTime: '2025-01-30 17:00',
		url: '/documents/evaluation-report.pdf',
	},
]);

// 获取文档类型文本
function getDocumentTypeText(type: string): string {
	const typeMap: Record<string, string> = {
		decision: '定标决定书',
		report: '评标报告',
		notice: '定标通知',
		contract: '合同文件',
		other: '其他文档',
	};
	return typeMap[type] || '未知类型';
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
	if (bytes === 0) return '0 B';
	const k = 1024;
	const sizes = ['B', 'KB', 'MB', 'GB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 处理保存
async function handleSave() {
	saving.value = true;
	try {
		// 模拟保存
		await new Promise(resolve => setTimeout(resolve, 1000));
		ElMessage.success('保存成功');
	} catch (error) {
		ElMessage.error('保存失败');
	} finally {
		saving.value = false;
	}
}

// 处理上传
function handleUpload() {
	uploadDialogVisible.value = true;
}

// 处理上传成功
function handleUploadSuccess(file: any) {
	documentList.value.push({
		id: Date.now().toString(),
		name: file.name,
		type: file.type || 'other',
		size: file.size,
		uploadTime: new Date().toLocaleString(),
		url: file.url,
	});
	ElMessage.success('文档上传成功');
}

// 处理下载
function handleDownload(document: any) {
	// 实现下载逻辑
	window.open(document.url, '_blank');
}

// 处理删除
async function handleDelete(document: any) {
	try {
		await ElMessageBox.confirm(
			`确定要删除文档"${document.name}"吗？`,
			'确认删除',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}
		);

		const index = documentList.value.findIndex(d => d.id === document.id);
		if (index > -1) {
			documentList.value.splice(index, 1);
			ElMessage.success('删除成功');
		}
	} catch {
		// 用户取消
	}
}

onMounted(() => {
	// 初始化数据
});
</script>

<style lang="scss" scoped>
.award-remark-container {
	padding: 20px;
	background: #fff;
	border-radius: 6px;
}

.remark-content {
	.remark-section,
	.history-section,
	.documents-section {
		margin-bottom: 32px;
		
		.section-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 16px;
			
			h3 {
				margin: 0;
				font-size: 16px;
				font-weight: 600;
				color: var(--Color-Text-text-color-primary, #1d2129);
			}
			
			.header-actions {
				display: flex;
				gap: 8px;
			}
		}
	}

	.history-section {
		.timeline-content {
			h4 {
				margin: 0 0 8px 0;
				font-size: 14px;
				font-weight: 600;
				color: #1d2129;
			}

			p {
				margin: 0 0 12px 0;
				font-size: 13px;
				color: #6b7280;
			}

			.record-details {
				margin-top: 8px;
			}
		}
	}
}
</style>
