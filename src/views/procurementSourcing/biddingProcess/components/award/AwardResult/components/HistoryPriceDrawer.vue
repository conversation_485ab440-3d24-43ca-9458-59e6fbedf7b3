<template>
	<yun-drawer
		v-model="visible"
		:title="`${materialName} - 历史报价`"
		size="large"
		destroy-on-close
		:show-cancel-button="false"
		confirm-button-text="关闭"
		@confirm="handleClose"
	>
		<div class="drawer-content">
			<el-table
				:data="historyData"
				style="width: 100%"
				border
				stripe
			>
				<el-table-column
					label="序号"
					type="index"
					width="60"
					align="center"
				/>
				<el-table-column
					label="项目名称"
					prop="projectName"
					min-width="200"
				/>
				<el-table-column
					label="供应商"
					prop="supplierName"
					min-width="150"
				/>
				<el-table-column
					label="报价时间"
					prop="quoteTime"
					width="150"
					align="center"
				/>
				<el-table-column
					label="出厂价"
					prop="factoryPrice"
					width="100"
					align="center"
				>
					<template #default="{ row }">
						<span class="price-text">{{ formatPrice(row.factoryPrice) }}</span>
					</template>
				</el-table-column>
				<el-table-column
					label="运费"
					prop="transportationCost"
					width="100"
					align="center"
				>
					<template #default="{ row }">
						<span class="price-text">{{ formatPrice(row.transportationCost) }}</span>
					</template>
				</el-table-column>
				<el-table-column
					label="到场价"
					prop="arrivalPrice"
					width="100"
					align="center"
				>
					<template #default="{ row }">
						<span class="price-text">{{ formatPrice(row.arrivalPrice) }}</span>
					</template>
				</el-table-column>
				<el-table-column
					label="是否中标"
					prop="isWinner"
					width="100"
					align="center"
				>
					<template #default="{ row }">
						<el-tag
							:type="row.isWinner ? 'success' : 'info'"
							size="small"
						>
							{{ row.isWinner ? '是' : '否' }}
						</el-tag>
					</template>
				</el-table-column>
			</el-table>
		</div>
	</yun-drawer>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';

// 组件属性
const visible = ref(false);
const materialName = ref('');
const materialCode = ref('');
const historyData = ref<any[]>([]);

// 格式化价格
function formatPrice(price: number): string {
	return `¥${price.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
}

// 处理关闭
function handleClose(done: () => void) {
	done();
}

// 显示抽屉
function show(row: any) {
	visible.value = true;
	materialName.value = row.materialName;
	materialCode.value = row.bidItemCode;
	
	// 模拟历史报价数据
	historyData.value = [
		{
			id: '1',
			projectName: 'XDMY-LYMG-20240710李原牧歌牧场原料采购项目',
			supplierName: '上海中华控股股份有限公司',
			quoteTime: '2024-07-15 14:30',
			factoryPrice: 230.00,
			transportationCost: 15.00,
			arrivalPrice: 245.00,
			isWinner: true,
		},
		{
			id: '2',
			projectName: 'XDMY-LYMG-20240610李原牧歌牧场原料采购项目',
			supplierName: '北京华润建材有限公司',
			quoteTime: '2024-06-20 10:15',
			factoryPrice: 240.00,
			transportationCost: 12.00,
			arrivalPrice: 252.00,
			isWinner: false,
		},
		{
			id: '3',
			projectName: 'XDMY-LYMG-20240510李原牧歌牧场原料采购项目',
			supplierName: '上海中华控股股份有限公司',
			quoteTime: '2024-05-18 16:45',
			factoryPrice: 225.00,
			transportationCost: 18.00,
			arrivalPrice: 243.00,
			isWinner: true,
		},
		{
			id: '4',
			projectName: 'XDMY-LYMG-20240410李原牧歌牧场原料采购项目',
			supplierName: '深圳建筑材料集团',
			quoteTime: '2024-04-22 09:30',
			factoryPrice: 235.00,
			transportationCost: 20.00,
			arrivalPrice: 255.00,
			isWinner: false,
		},
		{
			id: '5',
			projectName: 'XDMY-LYMG-20240310李原牧歌牧场原料采购项目',
			supplierName: '上海中华控股股份有限公司',
			quoteTime: '2024-03-25 11:20',
			factoryPrice: 220.00,
			transportationCost: 16.00,
			arrivalPrice: 236.00,
			isWinner: true,
		},
	];
}

// 暴露方法
defineExpose({
	show,
});
</script>

<style lang="scss" scoped>
.drawer-content {
	padding: 0;
}

.price-text {
	font-weight: 500;
	color: var(--Color-Primary-color-primary, #0069ff);
}
</style>
