<template>
	<yun-drawer
		v-model="visible"
		:title="`${materialName} - 价格趋势`"
		size="large"
		destroy-on-close
		:show-cancel-button="false"
		confirm-button-text="关闭"
		@confirm="handleClose"
	>
		<div class="drawer-content">
			<div class="trend-summary">
				<div class="summary-item">
					<div class="summary-label">当前价格</div>
					<div class="summary-value current-price">{{ formatPrice(currentPrice) }}</div>
				</div>
				<div class="summary-item">
					<div class="summary-label">平均价格</div>
					<div class="summary-value">{{ formatPrice(averagePrice) }}</div>
				</div>
				<div class="summary-item">
					<div class="summary-label">最高价格</div>
					<div class="summary-value high-price">{{ formatPrice(maxPrice) }}</div>
				</div>
				<div class="summary-item">
					<div class="summary-label">最低价格</div>
					<div class="summary-value low-price">{{ formatPrice(minPrice) }}</div>
				</div>
			</div>

			<el-table
				:data="trendData"
				style="width: 100%"
				border
				stripe
			>
				<el-table-column
					label="序号"
					type="index"
					width="60"
					align="center"
				/>
				<el-table-column
					label="统计时间"
					prop="statisticsTime"
					width="120"
					align="center"
				/>
				<el-table-column
					label="市场均价"
					prop="marketPrice"
					width="120"
					align="center"
				>
					<template #default="{ row }">
						<span class="price-text">{{ formatPrice(row.marketPrice) }}</span>
					</template>
				</el-table-column>
				<el-table-column
					label="最高价"
					prop="maxPrice"
					width="120"
					align="center"
				>
					<template #default="{ row }">
						<span class="high-price">{{ formatPrice(row.maxPrice) }}</span>
					</template>
				</el-table-column>
				<el-table-column
					label="最低价"
					prop="minPrice"
					width="120"
					align="center"
				>
					<template #default="{ row }">
						<span class="low-price">{{ formatPrice(row.minPrice) }}</span>
					</template>
				</el-table-column>
				<el-table-column
					label="价格波动"
					prop="priceChange"
					width="120"
					align="center"
				>
					<template #default="{ row }">
						<span 
							:class="['price-change', row.priceChange > 0 ? 'increase' : row.priceChange < 0 ? 'decrease' : 'stable']"
						>
							{{ row.priceChange > 0 ? '+' : '' }}{{ row.priceChange.toFixed(2) }}%
						</span>
					</template>
				</el-table-column>
				<el-table-column
					label="数据来源"
					prop="dataSource"
					min-width="150"
				/>
			</el-table>
		</div>
	</yun-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// 组件属性
const visible = ref(false);
const materialName = ref('');
const materialCode = ref('');
const currentPrice = ref(0);
const averagePrice = ref(0);
const maxPrice = ref(0);
const minPrice = ref(0);
const trendData = ref<any[]>([]);

// 格式化价格
function formatPrice(price: number): string {
	return `¥${price.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
}

// 处理关闭
function handleClose(done: () => void) {
	done();
}

// 显示抽屉
function show(row: any) {
	visible.value = true;
	materialName.value = row.materialName;
	materialCode.value = row.bidItemCode;
	currentPrice.value = row.arrivalPrice || 235.50;
	averagePrice.value = 245.20;
	maxPrice.value = 265.00;
	minPrice.value = 220.00;
	
	// 模拟价格趋势数据
	trendData.value = [
		{
			id: '1',
			statisticsTime: '2025-01',
			marketPrice: 245.20,
			maxPrice: 265.00,
			minPrice: 230.00,
			priceChange: 2.5,
			dataSource: '国家统计局、行业协会',
		},
		{
			id: '2',
			statisticsTime: '2024-12',
			marketPrice: 239.10,
			maxPrice: 258.00,
			minPrice: 225.00,
			priceChange: -1.8,
			dataSource: '国家统计局、行业协会',
		},
		{
			id: '3',
			statisticsTime: '2024-11',
			marketPrice: 243.50,
			maxPrice: 260.00,
			minPrice: 228.00,
			priceChange: 1.2,
			dataSource: '国家统计局、行业协会',
		},
		{
			id: '4',
			statisticsTime: '2024-10',
			marketPrice: 240.60,
			maxPrice: 255.00,
			minPrice: 220.00,
			priceChange: -0.5,
			dataSource: '国家统计局、行业协会',
		},
		{
			id: '5',
			statisticsTime: '2024-09',
			marketPrice: 241.80,
			maxPrice: 258.00,
			minPrice: 225.00,
			priceChange: 0.8,
			dataSource: '国家统计局、行业协会',
		},
		{
			id: '6',
			statisticsTime: '2024-08',
			marketPrice: 239.90,
			maxPrice: 252.00,
			minPrice: 222.00,
			priceChange: -2.1,
			dataSource: '国家统计局、行业协会',
		},
	];
}

// 暴露方法
defineExpose({
	show,
});
</script>

<style lang="scss" scoped>
.drawer-content {
	padding: 0;
}

.trend-summary {
	display: flex;
	gap: 20px;
	margin-bottom: 20px;
	padding: 16px;
	background: #f9fafb;
	border-radius: 6px;
	border: 1px solid #e5e7eb;
}

.summary-item {
	flex: 1;
	text-align: center;
}

.summary-label {
	font-size: 12px;
	color: #86909C;
	margin-bottom: 4px;
}

.summary-value {
	font-size: 16px;
	font-weight: 600;
	color: #1D2129;
	
	&.current-price {
		color: var(--Color-Primary-color-primary, #0069ff);
	}
	
	&.high-price {
		color: #ff4d4f;
	}
	
	&.low-price {
		color: #52c41a;
	}
}

.price-text {
	font-weight: 500;
	color: var(--Color-Primary-color-primary, #0069ff);
}

.price-change {
	font-weight: 500;
	
	&.increase {
		color: #ff4d4f;
	}
	
	&.decrease {
		color: #52c41a;
	}
	
	&.stable {
		color: #86909C;
	}
}

.high-price {
	color: #ff4d4f;
	font-weight: 500;
}

.low-price {
	color: #52c41a;
	font-weight: 500;
}
</style>
