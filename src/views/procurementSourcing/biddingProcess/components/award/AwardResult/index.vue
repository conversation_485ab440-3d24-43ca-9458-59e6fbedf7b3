<template>
	<div class="award-result-container">
		<!-- 定标基本信息 -->
		<div class="award-info-section">

			<div class="award-info-content">
				<div class="info-item">
					<div class="info-label">定标人</div>
					<div class="info-value">{{ awardInfo.awardPerson }}</div>
				</div>
				<div class="info-item">
					<div class="info-label">定标时间</div>
					<div class="info-value">{{ awardInfo.awardTime }}</div>
				</div>
				<div class="info-item">
					<div class="info-label">审批状态</div>
					<div class="info-value">
						<el-tag
							:type="getApprovalStatusType(awardInfo.approvalStatus)"
							size="small"
						>
							{{ getApprovalStatusText(awardInfo.approvalStatus) }}
						</el-tag>
					</div>
				</div>
			</div>
		</div>

		<!-- 定标清单 -->
		<div class="award-list-section">
			<div class="section-header">
				<div class="header-title">定标清单</div>
<!--				<div class="header-actions">-->
<!--					<el-button-->
<!--						type="primary"-->
<!--						size="small"-->
<!--						@click="handleExportList"-->
<!--					>-->
<!--						导出清单-->
<!--					</el-button>-->
			</div>

			<StageTabs
				:tabs="tabs"
				v-model="activeTabIndex"
			/>

			<div class="flex justify-between items-center my-4">
				<el-radio-group v-model="viewMode">
					<el-radio-button label="material">按物料查看</el-radio-button>
					<el-radio-button label="supplier">按供应商查看</el-radio-button>
				</el-radio-group>
				<div>
					已发起 3 轮报价
				</div>
			</div>
			<el-table
				:data="awardList"
				style="width: 100%"
				border
        class="editable-table"
			>
				<el-table-column
					label="序号"
					type="index"
					width="60"
					align="center"
				/>
				<el-table-column
					label="物料编码"
					prop="bidItemCode"
					width="120"
					align="center"
				/>
				<el-table-column
					label="物料名称"
					prop="materialName"
					min-width="150"
				/>
				<el-table-column
					label="规格型号"
					prop="specification"
					min-width="120"
				/>
				<el-table-column
					label="报价供应商数量"
					prop="supplierCount"
					width="130"
					align="center"
				/>
				<el-table-column
					label="报价供应商"
					prop="supplierName"
					min-width="200"
					align="center"
				/>
				<el-table-column
					label="报价轮次"
					prop="quoteRound"
					width="100"
					align="center"
				/>
				<el-table-column
					label="可供数量"
					prop="availableQuantity"
					width="100"
					align="center"
				/>
				<el-table-column
					label="出厂价"
					prop="factoryPrice"
					width="100"
					align="center"
				/>
				<el-table-column
					label="运费"
					prop="transportationCost"
					width="100"
					align="center"
				/>
				<el-table-column
					label="到场价"
					prop="arrivalPrice"
					width="100"
					align="center"
				/>
				<el-table-column
					label="总价"
					prop="totalPrice"
					width="100"
					align="center"
				/>
				<el-table-column
					label="是否中标"
					prop="isWinner"
					width="100"
					align="center"
					fixed="right"
				>
					<template #default="{ row }">
						<el-checkbox
							:model-value="row.isWinner"
						/>
					</template>
				</el-table-column>
				<el-table-column
					label="中标数量"
					prop="totalPrice"
					min-width="200"
					align="center"
					fixed="right"
				>
					<template #default="{ row }">
						<el-input
							style="width: 100%"
							placeholder="请输入"
							v-model="row.winnerQuantity"
							type="number"
							:disabled="!row.isWinner"
						/>
					</template>
				</el-table-column>
				<el-table-column
					label="历史报价"
					prop="historyPrice"
					width="100"
					align="center"
					fixed="right"
				>
					<template #default="{ row }">
						<el-button
							type="primary"
							size="small"
							@click="handleViewHistory(row)"
						>
							查看
						</el-button>
					</template>
				</el-table-column>
				<el-table-column
					label="物料价格趋势"
					prop="historyPrice"
					width="120"
					align="center"
					fixed="right"
				>
					<template #default="{ row }">
						<el-button
							type="primary"
							size="small"
							@click="handleViewPriceTrend(row)"
						>
							查看
						</el-button>
					</template>
				</el-table-column>
			</el-table>

			</div>


		<!-- 定标公告 -->
		<div class="award-announcement-section">
			<div class="section-header">
				<div class="header-title">定标公告</div>
				<div class="header-actions">
					<el-button
						type="primary"
						size="small"
						@click="handleEditAnnouncement"
					>
						编辑公告
					</el-button>
					<el-button
						type="success"
						size="small"
						@click="handlePublishAnnouncement"
					>
						发布公告
					</el-button>
				</div>
			</div>

			<div class="announcement-content">
				<el-form label-width="120px">
					<el-form-item label="公告标题">
						<span>{{ announcementInfo.title }}</span>
					</el-form-item>
					<el-form-item label="发布状态">
						<el-tag
							:type="getPublishStatusType(announcementInfo.publishStatus)"
							size="small"
						>
							{{ getPublishStatusText(announcementInfo.publishStatus) }}
						</el-tag>
					</el-form-item>
					<el-form-item label="发布时间">
						<span>{{ announcementInfo.publishTime || '未发布' }}</span>
					</el-form-item>
					<el-form-item label="异议期">
						<span>{{ announcementInfo.objectionPeriod }}</span>
					</el-form-item>
				</el-form>
			</div>
		</div>

		<!-- 定标附件 -->
		<div class="award-attachments-section">
			<div class="section-header">
				<div class="header-title">定标附件</div>
				<div class="header-actions">
					<el-button
						type="primary"
						size="small"
						@click="handleUploadAttachment"
					>
						上传附件
					</el-button>
				</div>
			</div>

			<div class="attachment-content">
				<div class="attachment-item">
					<div class="attachment-info">
						<div class="attachment-name">定标附件</div>
						<div class="attachment-meta">
							<span class="file-size">{{ formatFileSize(attachmentInfo.awardAttachment.size) }}</span>
							<span class="upload-time">{{ attachmentInfo.awardAttachment.uploadTime }}</span>
						</div>
					</div>
					<div class="attachment-actions">
						<el-button
							type="primary"
							size="small"
							@click="handleDownloadAwardAttachment"
							:disabled="!attachmentInfo.awardAttachment.url"
						>
							下载
						</el-button>
						<el-button
							type="primary"
							size="small"
							@click="handleUploadAwardAttachment"
						>
							{{ attachmentInfo.awardAttachment.url ? '重新上传' : '上传' }}
						</el-button>
					</div>
				</div>

				<div class="attachment-item">
					<div class="attachment-info">
						<div class="attachment-name">定标结果附件</div>
						<div class="attachment-meta">
							<span class="file-size">{{ formatFileSize(attachmentInfo.resultAttachment.size) }}</span>
							<span class="upload-time">{{ attachmentInfo.resultAttachment.uploadTime }}</span>
						</div>
					</div>
					<div class="attachment-actions">
						<el-button
							type="primary"
							size="small"
							@click="handleDownloadResultAttachment"
							:disabled="!attachmentInfo.resultAttachment.url"
						>
							下载
						</el-button>
						<el-button
							type="primary"
							size="small"
							@click="handleUploadResultAttachment"
						>
							{{ attachmentInfo.resultAttachment.url ? '重新上传' : '上传' }}
						</el-button>
					</div>
				</div>
			</div>
		</div>

		<!-- 定标备注 -->
		<div class="award-remark-section">
			<div class="section-header">
				<div class="header-title">定标备注</div>
				<div class="header-actions">
					<el-button
						type="primary"
						size="small"
						@click="handleSaveRemark"
						:loading="saving"
					>
						保存
					</el-button>
				</div>
			</div>

			<el-form label-width="120px">
				<el-form-item label="定标说明">
					<el-input
						v-model="remarkInfo.description"
						type="textarea"
						:rows="4"
						placeholder="请输入定标说明"
						maxlength="1000"
						show-word-limit
					/>
				</el-form-item>
				<el-form-item label="备注信息">
					<el-input
						v-model="remarkInfo.remark"
						type="textarea"
						:rows="3"
						placeholder="请输入备注信息（选填）"
						maxlength="500"
						show-word-limit
					/>
				</el-form-item>
			</el-form>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import StageTabs from '@/views/procurementSourcing/biddingProcess/components/bidding/StageTabs.vue';

// 保存状态
const saving = ref(false);

// 定标基本信息
const awardInfo = reactive({
	awardPerson: '王小二',
	awardTime: '2025-01-31 12:22:00',
	approvalStatus: 'pending', // pending, approved, rejected
	projectName: 'XDMY-LYMG-20240810李原牧歌牧场原料采购项目',
	projectCode: 'XDMY-LYMG-20240810',
	procurementMethod: '公开询价',
	budgetAmount: 500000,
	awardAmount: 471000,
});

const tabs = [
	{ key: '1', label: '标段01: 物资采购物资采购...' },
	{ key: '2', label: '标段02: 物资采购物资' },
	{ key: '3', label: '标段03: 施工队伍安排' },
	{ key: '4', label: '标段04: 预算编制' },
];

const activeTabIndex = ref(0);
const viewMode = ref('material'); // 'material' | 'supplier'

// 定标清单
const awardList = ref([
	{
		id: '1',
		bidItemCode: 'ASW12345',
		materialName: '特种合金01',
		specification: '规格合金01',
		quantity: 2000,
		winnerName: '上海中华控股股份有限公司',
		winnerPrice: 235.50,
		budgetPrice: 250.00,
		isWinner: true
	},
	{
		id: '2',
		bidItemCode: 'ASW12345',
		materialName: '特种合金01',
		specification: '规格合金01',
		quantity: 2000,
		winnerName: '上海中华控股股份有限公司',
		winnerPrice: 235.50,
		budgetPrice: 250.00,
		isWinner: false
	},
	// 可以添加更多数据...
]);

// 公告信息
const announcementInfo = reactive({
	title: 'XDMY-LYMG-20240810李原牧歌牧场原料采购项目定标公告',
	publishStatus: 'draft', // draft, published, cancelled
	publishTime: '',
	objectionPeriod: '2025-02-01 至 2025-02-08 (7个工作日)',
});

// 附件信息
const attachmentInfo = reactive({
	awardAttachment: {
		name: '定标附件.pdf',
		size: 1024 * 1024 * 2.5,
		uploadTime: '2025-01-31 12:30',
		url: '/documents/award-attachment.pdf',
	},
	resultAttachment: {
		name: '定标结果附件.pdf',
		size: 1024 * 1024 * 3.8,
		uploadTime: '2025-01-31 14:15',
		url: '/documents/award-result.pdf',
	},
});

// 备注信息
const remarkInfo = reactive({
	description: `经过公开、公平、公正的评标程序，根据评标委员会的评审意见，现确定以下定标结果：

1. 标的编号ASW12345（特种合金01）：上海中华控股股份有限公司中标，中标价格¥235.50。

2. 定标依据：
   - 符合招标文件的技术要求
   - 报价合理，性价比最优
   - 企业资质齐全，信誉良好
   - 供货能力满足项目需求`,
	remark: '定标过程规范，结果公正合理。',
});

// 格式化价格
function formatPrice(price: number): string {
	return `¥${price.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
}

// 格式化节约金额
function formatSavings(row: any): string {
	const savings = row.budgetPrice - row.winnerPrice;
	return savings > 0 ? `¥${savings.toFixed(2)}` : '¥0.00';
}

// 格式化节约率
function formatSavingsRate(row: any): string {
	const savings = row.budgetPrice - row.winnerPrice;
	const rate = savings > 0 ? (savings / row.budgetPrice) * 100 : 0;
	return `${rate.toFixed(1)}%`;
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
	if (bytes === 0) return '0 B';
	const k = 1024;
	const sizes = ['B', 'KB', 'MB', 'GB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 获取审批状态类型
function getApprovalStatusType(status: string): string {
	const typeMap: Record<string, string> = {
		pending: 'warning',
		approved: 'success',
		rejected: 'danger',
	};
	return typeMap[status] || '';
}

// 获取审批状态文本
function getApprovalStatusText(status: string): string {
	const textMap: Record<string, string> = {
		pending: '待审批',
		approved: '已审批',
		rejected: '已驳回',
	};
	return textMap[status] || '未知';
}

// 获取发布状态类型
function getPublishStatusType(status: string): string {
	const typeMap: Record<string, string> = {
		draft: 'info',
		published: 'success',
		cancelled: 'danger',
	};
	return typeMap[status] || '';
}

// 获取发布状态文本
function getPublishStatusText(status: string): string {
	const textMap: Record<string, string> = {
		draft: '草稿',
		published: '已发布',
		cancelled: '已取消',
	};
	return textMap[status] || '未知';
}

// 获取附件类型文本
function getAttachmentTypeText(type: string): string {
	const typeMap: Record<string, string> = {
		decision: '定标决定书',
		report: '评标报告',
		notice: '通知文件',
		contract: '合同文件',
		other: '其他',
	};
	return typeMap[type] || '未知类型';
}

// 处理编辑
function handleEdit() {
	ElMessage.info('编辑功能开发中');
}

// 处理提交审批
async function handleSubmitApproval() {
	try {
		await ElMessageBox.confirm(
			'确定要提交审批吗？提交后将无法修改。',
			'确认提交',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}
		);

		awardInfo.approvalStatus = 'pending';
		ElMessage.success('已提交审批');
	} catch {
		// 用户取消
	}
}

// 处理导出清单
function handleExportList() {
	ElMessage.success('开始导出定标清单');
}

// 处理编辑公告
function handleEditAnnouncement() {
	ElMessage.info('编辑公告功能开发中');
}

// 处理发布公告
function handlePublishAnnouncement() {
	announcementInfo.publishStatus = 'published';
	announcementInfo.publishTime = new Date().toLocaleString();
	ElMessage.success('公告发布成功');
}

// 处理上传定标附件
function handleUploadAwardAttachment() {
	ElMessage.info('上传定标附件功能开发中');
}

// 处理下载定标附件
function handleDownloadAwardAttachment() {
	if (attachmentInfo.awardAttachment.url) {
		window.open(attachmentInfo.awardAttachment.url, '_blank');
	} else {
		ElMessage.warning('附件不存在');
	}
}

// 处理上传定标结果附件
function handleUploadResultAttachment() {
	ElMessage.info('上传定标结果附件功能开发中');
}

// 处理下载定标结果附件
function handleDownloadResultAttachment() {
	if (attachmentInfo.resultAttachment.url) {
		window.open(attachmentInfo.resultAttachment.url, '_blank');
	} else {
		ElMessage.warning('附件不存在');
	}
}

// 处理保存备注
async function handleSaveRemark() {
	saving.value = true;
	try {
		// 模拟保存
		await new Promise(resolve => setTimeout(resolve, 1000));
		ElMessage.success('保存成功');
	} catch (error) {
		ElMessage.error('保存失败');
	} finally {
		saving.value = false;
	}
}
</script>

<style lang="scss" scoped>
.award-result-container {
	//padding: 20px;
	//background: #fff;
	//border-radius: 6px;
}

.award-info-section,
.award-list-section,
.award-announcement-section,
.award-attachments-section,
.award-remark-section {
	margin-bottom: 12px;
	background: #fff;
	border-radius: 6px;
	padding: 20px;

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16px;

		h3 {
			margin: 0;
			font-size: 16px;
			font-weight: 600;
			color: var(--Color-Text-text-color-primary, #1d2129);
		}

		.header-actions {
			display: flex;
			gap: 8px;
		}
	}
}

.announcement-content {
	background: #f9fafb;
	padding: 16px;
	border-radius: 6px;
	border: 1px solid #e5e7eb;
}

.price-text {
	font-weight: 500;
	color: var(--Color-Primary-color-primary, #0069ff);
}

.savings-text {
	font-weight: 500;
	color: #52c41a;
}

.savings-rate {
	font-weight: 500;
	color: #52c41a;
}

.award-info-content {
	display: flex;
	gap: 20px;
	flex-wrap: wrap;
}

.award-info-section {
	padding: 13px 20px;
	background: #fff url("../assets/img/head-bg.png") no-repeat right 0 / 599.76px 204px;
}

.info-item {
	display: flex;
	flex-direction: row;
	gap: 8px;
}

.info-label {
	color: #86909C;
	font-size: 14px;
	line-height: 20px;
}

.info-value {
	color: #1D2129;
	font-size: 14px;
	line-height: 20px;
	font-weight: 400;
}

.attachment-content {
	display: flex;
	flex-direction: column;
	gap: 16px;
}

.attachment-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px;
	background: #f9fafb;
	border-radius: 6px;
	border: 1px solid #e5e7eb;
}

.attachment-info {
	flex: 1;
}

.attachment-name {
	font-size: 14px;
	font-weight: 500;
	color: #1D2129;
	margin-bottom: 4px;
}

.attachment-meta {
	display: flex;
	gap: 16px;
	font-size: 12px;
	color: #86909C;
}

.attachment-actions {
	display: flex;
	gap: 8px;
}

.editable-table {
  :deep(.el-table__header) {
    th {
      background: var(--Color-Fill-fill-color-light, #f5f7fa);
      padding: 6px 0;
      border-bottom: 1px solid var(--Color-Border-border-color-light, #EBEEF5);

      .cell {
        color: var(---el-text-color-regular, #505762);
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
      }
    }
  }

  :deep(.el-table__body) {
    tr {
      &:hover {
        background-color: #fff !important;

        td {
          background-color: #fff !important;
        }
      }

      td {
        border-bottom: 1px solid #EBEEF5;
        padding: 6px 0;
      }
    }
  }

  // 确保 Element Plus 的 hover 类也被覆盖
  :deep(.el-table__row) {
    &:hover {
      background-color: #fff !important;

      td {
        background-color: #fff !important;
      }
    }

    &.hover-row {
      background-color: transparent !important;
    }

    td {
      &.hover-cell {
        background-color: transparent !important;
      }
    }
  }

  // 表格内的控件样式
  .table-select,
  .table-input {
    width: 100%;

    :deep(.el-input__wrapper) {
      background-color: transparent !important;
      border: 1px solid transparent !important;
      box-shadow: none !important;
      padding: 4px 8px;

      &:hover,
      &:focus {
        border-color: var(--Color-Primary-color-primary, #0069ff) !important;
        background-color: #fff !important;
      }
    }

    :deep(.el-input__inner) {
      border-radius: var(--Radius-border-radius-small, 2px) !important;
      background: var(--Color-Fill-fill-color-light, #F5F7FA) !important;
      border: none !important;
      color: #1D2129 !important;
      font-size: 14px;
      height: 24px;

      &::placeholder {
        color: var(--Color-Text-text-color-regular, #4E5969);
        font-family: "PingFang SC";
        font-size: 14px;
      }
    }
  }

  // 操作按钮样式
  .table-actions {
    display: flex;
    gap: 8px;
    align-items: center;

    .action-btn {
      padding: 4px 8px;
      height: 28px;
      font-size: 12px;
      border-radius: 4px;

      &.el-button--primary {
        background: var(--Color-Primary-color-primary, #0069ff);
        border-color: var(--Color-Primary-color-primary, #0069ff);

        &:hover {
          background: #1677ff;
          border-color: #1677ff;
        }
      }

      &.el-button--danger {
        background: #ff4d4f;
        border-color: #ff4d4f;

        &:hover {
          background: #ff7875;
          border-color: #ff7875;
        }
      }
    }
  }
}
</style>
