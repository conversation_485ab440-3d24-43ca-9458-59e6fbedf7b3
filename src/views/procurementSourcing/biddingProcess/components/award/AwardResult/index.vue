<template>
	<div class="award-result-container">
		<!-- 定标基本信息 -->
		<div class="award-info-section">

			<el-descriptions :column="3" border>
				<el-descriptions-item label="定标人">{{ awardInfo.awardPerson }}</el-descriptions-item>
				<el-descriptions-item label="定标时间">{{ awardInfo.awardTime }}</el-descriptions-item>
				<el-descriptions-item label="审批状态">
					<el-tag
						:type="getApprovalStatusType(awardInfo.approvalStatus)"
						size="small"
					>
						{{ getApprovalStatusText(awardInfo.approvalStatus) }}
					</el-tag>
				</el-descriptions-item>
				<el-descriptions-item label="项目名称" :span="2">{{ awardInfo.projectName }}</el-descriptions-item>
				<el-descriptions-item label="项目编号">{{ awardInfo.projectCode }}</el-descriptions-item>
				<el-descriptions-item label="采购方式">{{ awardInfo.procurementMethod }}</el-descriptions-item>
				<el-descriptions-item label="预算金额">{{ formatPrice(awardInfo.budgetAmount) }}</el-descriptions-item>
				<el-descriptions-item label="中标金额">{{ formatPrice(awardInfo.awardAmount) }}</el-descriptions-item>
			</el-descriptions>
		</div>

		<!-- 定标清单 -->
		<div class="award-list-section">
			<div class="section-header">
				<h3>定标清单</h3>
				<div class="header-actions">
					<el-button
						type="primary"
						size="small"
						@click="handleExportList"
					>
						导出清单
					</el-button>
				</div>
			</div>

			<el-table
				:data="awardList"
				style="width: 100%"
				border
				stripe
			>
				<el-table-column
					label="序号"
					type="index"
					width="60"
					align="center"
				/>
				<el-table-column
					label="标的编号"
					prop="bidItemCode"
					width="120"
					align="center"
				/>
				<el-table-column
					label="物料名称"
					prop="materialName"
					min-width="150"
				/>
				<el-table-column
					label="规格型号"
					prop="specification"
					min-width="120"
				/>
				<el-table-column
					label="需求数量"
					prop="quantity"
					width="100"
					align="center"
				/>
				<el-table-column
					label="中标供应商"
					prop="winnerName"
					min-width="200"
				/>
				<el-table-column
					label="中标价格"
					prop="winnerPrice"
					width="120"
					align="center"
				>
					<template #default="{ row }">
						<span class="price-text">{{ formatPrice(row.winnerPrice) }}</span>
					</template>
				</el-table-column>
				<el-table-column
					label="节约金额"
					width="120"
					align="center"
				>
					<template #default="{ row }">
						<span class="savings-text">{{ formatSavings(row) }}</span>
					</template>
				</el-table-column>
				<el-table-column
					label="节约率"
					width="100"
					align="center"
				>
					<template #default="{ row }">
						<span class="savings-rate">{{ formatSavingsRate(row) }}</span>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<!-- 定标公告 -->
		<div class="award-announcement-section">
			<div class="section-header">
				<h3>定标公告</h3>
				<div class="header-actions">
					<el-button
						type="primary"
						size="small"
						@click="handleEditAnnouncement"
					>
						编辑公告
					</el-button>
					<el-button
						type="success"
						size="small"
						@click="handlePublishAnnouncement"
					>
						发布公告
					</el-button>
				</div>
			</div>

			<div class="announcement-content">
				<el-form label-width="120px">
					<el-form-item label="公告标题">
						<span>{{ announcementInfo.title }}</span>
					</el-form-item>
					<el-form-item label="发布状态">
						<el-tag
							:type="getPublishStatusType(announcementInfo.publishStatus)"
							size="small"
						>
							{{ getPublishStatusText(announcementInfo.publishStatus) }}
						</el-tag>
					</el-form-item>
					<el-form-item label="发布时间">
						<span>{{ announcementInfo.publishTime || '未发布' }}</span>
					</el-form-item>
					<el-form-item label="异议期">
						<span>{{ announcementInfo.objectionPeriod }}</span>
					</el-form-item>
				</el-form>
			</div>
		</div>

		<!-- 定标附件 -->
		<div class="award-attachments-section">
			<div class="section-header">
				<h3>定标附件</h3>
				<div class="header-actions">
					<el-button
						type="primary"
						size="small"
						@click="handleUploadAttachment"
					>
						上传附件
					</el-button>
				</div>
			</div>

			<el-table
				:data="attachmentList"
				style="width: 100%"
				border
				stripe
			>
				<el-table-column
					label="序号"
					type="index"
					width="60"
					align="center"
				/>
				<el-table-column
					label="附件名称"
					prop="name"
					min-width="200"
				/>
				<el-table-column
					label="附件类型"
					prop="type"
					width="120"
					align="center"
				>
					<template #default="{ row }">
						<el-tag size="small">{{ getAttachmentTypeText(row.type) }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column
					label="文件大小"
					prop="size"
					width="100"
					align="center"
				>
					<template #default="{ row }">
						{{ formatFileSize(row.size) }}
					</template>
				</el-table-column>
				<el-table-column
					label="上传时间"
					prop="uploadTime"
					width="150"
					align="center"
				/>
				<el-table-column
					label="操作"
					width="150"
					align="center"
					fixed="right"
				>
					<template #default="{ row }">
						<el-button
							type="primary"
							size="small"
							@click="handleDownload(row)"
						>
							下载
						</el-button>
						<el-button
							type="danger"
							size="small"
							@click="handleDeleteAttachment(row)"
						>
							删除
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<!-- 定标备注 -->
		<div class="award-remark-section">
			<div class="section-header">
				<h3>定标备注</h3>
				<div class="header-actions">
					<el-button
						type="primary"
						size="small"
						@click="handleSaveRemark"
						:loading="saving"
					>
						保存
					</el-button>
				</div>
			</div>

			<el-form label-width="120px">
				<el-form-item label="定标说明">
					<el-input
						v-model="remarkInfo.description"
						type="textarea"
						:rows="4"
						placeholder="请输入定标说明"
						maxlength="1000"
						show-word-limit
					/>
				</el-form-item>
				<el-form-item label="备注信息">
					<el-input
						v-model="remarkInfo.remark"
						type="textarea"
						:rows="3"
						placeholder="请输入备注信息（选填）"
						maxlength="500"
						show-word-limit
					/>
				</el-form-item>
			</el-form>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

// 保存状态
const saving = ref(false);

// 定标基本信息
const awardInfo = reactive({
	awardPerson: '王小二',
	awardTime: '2025-01-31 12:22:00',
	approvalStatus: 'pending', // pending, approved, rejected
	projectName: 'XDMY-LYMG-20240810李原牧歌牧场原料采购项目',
	projectCode: 'XDMY-LYMG-20240810',
	procurementMethod: '公开询价',
	budgetAmount: 500000,
	awardAmount: 471000,
});

// 定标清单
const awardList = ref([
	{
		id: '1',
		bidItemCode: 'ASW12345',
		materialName: '特种合金01',
		specification: '规格合金01',
		quantity: 2000,
		winnerName: '上海中华控股股份有限公司',
		winnerPrice: 235.50,
		budgetPrice: 250.00,
	},
	// 可以添加更多数据...
]);

// 公告信息
const announcementInfo = reactive({
	title: 'XDMY-LYMG-20240810李原牧歌牧场原料采购项目定标公告',
	publishStatus: 'draft', // draft, published, cancelled
	publishTime: '',
	objectionPeriod: '2025-02-01 至 2025-02-08 (7个工作日)',
});

// 附件列表
const attachmentList = ref([
	{
		id: '1',
		name: '定标决定书.pdf',
		type: 'decision',
		size: 1024 * 1024 * 2.5,
		uploadTime: '2025-01-31 12:30',
		url: '/documents/award-decision.pdf',
	},
	{
		id: '2',
		name: '评标报告.pdf',
		type: 'report',
		size: 1024 * 1024 * 5.2,
		uploadTime: '2025-01-30 17:00',
		url: '/documents/evaluation-report.pdf',
	},
]);

// 备注信息
const remarkInfo = reactive({
	description: `经过公开、公平、公正的评标程序，根据评标委员会的评审意见，现确定以下定标结果：

1. 标的编号ASW12345（特种合金01）：上海中华控股股份有限公司中标，中标价格¥235.50。

2. 定标依据：
   - 符合招标文件的技术要求
   - 报价合理，性价比最优
   - 企业资质齐全，信誉良好
   - 供货能力满足项目需求`,
	remark: '定标过程规范，结果公正合理。',
});

// 格式化价格
function formatPrice(price: number): string {
	return `¥${price.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
}

// 格式化节约金额
function formatSavings(row: any): string {
	const savings = row.budgetPrice - row.winnerPrice;
	return savings > 0 ? `¥${savings.toFixed(2)}` : '¥0.00';
}

// 格式化节约率
function formatSavingsRate(row: any): string {
	const savings = row.budgetPrice - row.winnerPrice;
	const rate = savings > 0 ? (savings / row.budgetPrice) * 100 : 0;
	return `${rate.toFixed(1)}%`;
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
	if (bytes === 0) return '0 B';
	const k = 1024;
	const sizes = ['B', 'KB', 'MB', 'GB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 获取审批状态类型
function getApprovalStatusType(status: string): string {
	const typeMap: Record<string, string> = {
		pending: 'warning',
		approved: 'success',
		rejected: 'danger',
	};
	return typeMap[status] || '';
}

// 获取审批状态文本
function getApprovalStatusText(status: string): string {
	const textMap: Record<string, string> = {
		pending: '待审批',
		approved: '已审批',
		rejected: '已驳回',
	};
	return textMap[status] || '未知';
}

// 获取发布状态类型
function getPublishStatusType(status: string): string {
	const typeMap: Record<string, string> = {
		draft: 'info',
		published: 'success',
		cancelled: 'danger',
	};
	return typeMap[status] || '';
}

// 获取发布状态文本
function getPublishStatusText(status: string): string {
	const textMap: Record<string, string> = {
		draft: '草稿',
		published: '已发布',
		cancelled: '已取消',
	};
	return textMap[status] || '未知';
}

// 获取附件类型文本
function getAttachmentTypeText(type: string): string {
	const typeMap: Record<string, string> = {
		decision: '定标决定书',
		report: '评标报告',
		notice: '通知文件',
		contract: '合同文件',
		other: '其他',
	};
	return typeMap[type] || '未知类型';
}

// 处理编辑
function handleEdit() {
	ElMessage.info('编辑功能开发中');
}

// 处理提交审批
async function handleSubmitApproval() {
	try {
		await ElMessageBox.confirm(
			'确定要提交审批吗？提交后将无法修改。',
			'确认提交',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}
		);

		awardInfo.approvalStatus = 'pending';
		ElMessage.success('已提交审批');
	} catch {
		// 用户取消
	}
}

// 处理导出清单
function handleExportList() {
	ElMessage.success('开始导出定标清单');
}

// 处理编辑公告
function handleEditAnnouncement() {
	ElMessage.info('编辑公告功能开发中');
}

// 处理发布公告
function handlePublishAnnouncement() {
	announcementInfo.publishStatus = 'published';
	announcementInfo.publishTime = new Date().toLocaleString();
	ElMessage.success('公告发布成功');
}

// 处理上传附件
function handleUploadAttachment() {
	ElMessage.info('上传附件功能开发中');
}

// 处理下载
function handleDownload(attachment: any) {
	window.open(attachment.url, '_blank');
}

// 处理删除附件
async function handleDeleteAttachment(attachment: any) {
	try {
		await ElMessageBox.confirm(
			`确定要删除附件"${attachment.name}"吗？`,
			'确认删除',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}
		);

		const index = attachmentList.value.findIndex(item => item.id === attachment.id);
		if (index > -1) {
			attachmentList.value.splice(index, 1);
			ElMessage.success('删除成功');
		}
	} catch {
		// 用户取消
	}
}

// 处理保存备注
async function handleSaveRemark() {
	saving.value = true;
	try {
		// 模拟保存
		await new Promise(resolve => setTimeout(resolve, 1000));
		ElMessage.success('保存成功');
	} catch (error) {
		ElMessage.error('保存失败');
	} finally {
		saving.value = false;
	}
}
</script>

<style lang="scss" scoped>
.award-result-container {
	padding: 20px;
	background: #fff;
	border-radius: 6px;
}

.award-info-section,
.award-list-section,
.award-announcement-section,
.award-attachments-section,
.award-remark-section {
	margin-bottom: 32px;

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16px;

		h3 {
			margin: 0;
			font-size: 16px;
			font-weight: 600;
			color: var(--Color-Text-text-color-primary, #1d2129);
		}

		.header-actions {
			display: flex;
			gap: 8px;
		}
	}
}

.announcement-content {
	background: #f9fafb;
	padding: 16px;
	border-radius: 6px;
	border: 1px solid #e5e7eb;
}

.price-text {
	font-weight: 500;
	color: var(--Color-Primary-color-primary, #0069ff);
}

.savings-text {
	font-weight: 500;
	color: #52c41a;
}

.savings-rate {
	font-weight: 500;
	color: #52c41a;
}
</style>
