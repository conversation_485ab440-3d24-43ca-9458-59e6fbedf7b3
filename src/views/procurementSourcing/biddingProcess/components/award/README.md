# 定标功能模块

## 功能概述

定标功能模块是招投标流程管理系统的核心组成部分，按照政府采购流程规范，分为四个主要阶段：定标结果、中标公示、中标公告、中标通知书。

## 模块结构

```
award/
├── AwardManagement/           # 定标管理主组件
│   └── index.vue
├── AwardResult/              # 定标结果
│   └── index.vue
├── WinnerPublicity/          # 中标公示
│   └── index.vue
├── WinnerAnnouncement/       # 中标公告
│   ├── index.vue
│   └── AnnouncementPreviewDialog.vue
├── WinnerNotification/       # 中标通知书
│   ├── index.vue
│   └── NotificationPreviewDialog.vue
└── README.md
```

## 主要功能

### 1. 定标结果 (AwardResult)

**功能特性：**
- 📋 定标基本信息展示（定标人、时间、审批状态）
- 📊 定标清单管理和导出
- 📢 定标公告信息展示
- 📎 定标附件管理
- 📝 定标备注和说明记录

**核心组件：**
- `AwardResult/index.vue` - 主界面，集成所有定标结果信息

### 2. 中标公示 (WinnerPublicity)

**功能特性：**
- 🏢 公示公司信息管理
- 📋 公示要求配置
- 📝 公示详情编辑
- 📎 公示附件管理
- 🌐 多平台发布管理

**核心组件：**
- `WinnerPublicity/index.vue` - 主界面

### 3. 中标公告 (WinnerAnnouncement)

**功能特性：**
- 🏆 中标供应商信息展示
- 📋 公告要求管理
- 📝 公告详情编辑
- 🌐 公告平台发布管理
- 👁️ 公告预览功能

**核心组件：**
- `WinnerAnnouncement/index.vue` - 主界面
- `AnnouncementPreviewDialog.vue` - 公告预览弹窗

### 4. 中标通知书 (WinnerNotification)

**功能特性：**
- 🏢 中标公司选择和管理
- ✏️ 可编辑的中标通知书模板
- 📧 多种发送方式（邮件、快递、系统）
- 📋 发送记录跟踪
- 👁️ 通知书预览和打印

**核心组件：**
- `WinnerNotification/index.vue` - 主界面
- `NotificationPreviewDialog.vue` - 通知书预览弹窗

## 技术特性

### 1. 组件化设计
- 每个功能模块独立封装
- 可复用的弹窗组件
- 统一的数据类型定义

### 2. 响应式布局
- 适配不同屏幕尺寸
- 移动端友好的交互设计
- 灵活的表格和表单布局

### 3. 用户体验优化
- 直观的步骤指示器
- 实时的状态反馈
- 友好的错误提示

### 4. 数据管理
- TypeScript类型安全
- 模拟数据支持开发测试
- 预留API接口集成点

## 使用方法

### 1. 在主流程中集成

```vue
<script setup lang="ts">
import AwardManagement from './components/award/AwardManagement/index.vue';

// 在组件映射中添加
const componentMap = {
  4: AwardManagement, // 定标阶段
};
</script>
```

### 2. 独立使用组件

```vue
<template>
  <AwardDecision />
</template>

<script setup lang="ts">
import AwardDecision from './components/award/AwardDecision/index.vue';
</script>
```

## 数据流

### 1. 定标流程
```
选择中标供应商 → 生成定标公告 → 发送通知 → 管理附件 → 记录备注
```

### 2. 状态管理
- 使用Vue 3 Composition API
- 响应式数据绑定
- 组件间通信通过props和events

### 3. 数据持久化
- 表单数据自动保存
- 文件上传状态管理
- 操作历史记录

## 扩展性

### 1. 新增功能模块
- 在award目录下创建新的组件文件夹
- 在AwardManagement中注册新组件
- 更新步骤配置

### 2. 自定义业务逻辑
- 修改types.ts中的数据类型
- 扩展API接口调用
- 添加新的验证规则

### 3. 样式定制
- 使用SCSS变量系统
- 支持主题切换
- 响应式断点配置

## 注意事项

1. **文件上传限制**
   - 单个文件最大20MB
   - 支持的文件格式有限制
   - 需要后端API支持

2. **浏览器兼容性**
   - 现代浏览器支持
   - PDF预览需要浏览器原生支持
   - 某些功能需要HTTPS环境

3. **性能优化**
   - 大文件上传使用分片
   - 表格数据虚拟滚动
   - 图片懒加载

## 开发计划

- [ ] 添加更多文件预览格式支持
- [ ] 实现拖拽上传功能
- [ ] 添加数据导出功能
- [ ] 集成电子签名功能
- [ ] 添加移动端适配
- [ ] 实现离线缓存功能

---

**版本**: v1.0.0  
**最后更新**: 2025-01-31  
**维护者**: 开发团队
