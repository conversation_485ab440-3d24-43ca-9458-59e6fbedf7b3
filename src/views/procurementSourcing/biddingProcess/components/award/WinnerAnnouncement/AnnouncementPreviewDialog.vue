<template>
	<el-dialog
		v-model="visible"
		title="公告预览"
		width="900px"
		:before-close="handleClose"
	>
		<div class="announcement-preview">
			<!-- 公告标题 -->
			<div class="announcement-header">
				<h2 class="announcement-title">{{ announcementData.title }}</h2>
				<div class="announcement-meta">
					<span>发布时间：{{ formatDateTime(announcementData.publishTime) }}</span>
				</div>
			</div>

			<!-- 公告内容 -->
			<div class="announcement-body">
				<div class="content-section">
					<pre class="announcement-content">{{ announcementData.content }}</pre>
				</div>

				<!-- 中标结果表格 -->
				<div class="winner-table-section">
					<h4>中标结果明细</h4>
					<table class="winner-table">
						<thead>
							<tr>
								<th>序号</th>
								<th>标的编号</th>
								<th>物料名称</th>
								<th>中标供应商</th>
								<th>中标价格</th>
								<th>节约金额</th>
								<th>节约率</th>
							</tr>
						</thead>
						<tbody>
							<tr
								v-for="(item, index) in winnerResults"
								:key="item.bidItemCode"
							>
								<td>{{ index + 1 }}</td>
								<td>{{ item.bidItemCode }}</td>
								<td>{{ item.materialName }}</td>
								<td>{{ item.winnerName }}</td>
								<td class="price-cell">{{ formatPrice(item.winnerPrice) }}</td>
								<td class="savings-cell">{{ formatSavings(item) }}</td>
								<td class="rate-cell">{{ formatSavingsRate(item) }}</td>
							</tr>
						</tbody>
					</table>
				</div>

				<!-- 异议期说明 -->
				<div class="objection-section">
					<h4>异议期说明</h4>
					<p>
						异议期：{{ formatDateTime(announcementData.objectionStartTime) }} 至 
						{{ formatDateTime(announcementData.objectionEndTime) }}
					</p>
					<p>
						供应商对本次定标结果有异议的，可在异议期内以书面形式向采购人提出质疑。
						逾期提出的质疑将不予受理。
					</p>
				</div>

				<!-- 联系方式 -->
				<div class="contact-section">
					<h4>联系方式</h4>
					<div class="contact-info">
						<p><strong>联系人：</strong>{{ contactInfo.person }}</p>
						<p><strong>联系电话：</strong>{{ contactInfo.phone }}</p>
						<p><strong>邮箱：</strong>{{ contactInfo.email }}</p>
						<p><strong>地址：</strong>{{ contactInfo.address }}</p>
					</div>
				</div>

				<!-- 发布渠道 -->
				<div class="publish-channels-section">
					<h4>发布渠道</h4>
					<div class="channels-list">
						<el-tag
							v-for="channel in getPublishChannelLabels()"
							:key="channel"
							type="info"
							size="small"
							class="channel-tag"
						>
							{{ channel }}
						</el-tag>
					</div>
				</div>
			</div>

			<!-- 公告尾部 -->
			<div class="announcement-footer">
				<div class="signature">
					<p>采购人：XDMY-LYMG-20240810李原牧歌牧场</p>
					<p>{{ formatDate(announcementData.publishTime) }}</p>
				</div>
			</div>
		</div>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="handleClose">关闭</el-button>
				<el-button type="primary" @click="handlePrint">打印</el-button>
				<el-button type="success" @click="handleDownload">下载PDF</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import type { WinnerInfo } from '../AwardDecision/types';

interface Props {
	visible: boolean;
	announcementData: any;
	winnerResults: WinnerInfo[];
	contactInfo: any;
}

interface Emits {
	(e: 'update:visible', value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(props.visible);

// 监听visible变化
watch(() => props.visible, (newVal) => {
	visible.value = newVal;
});

watch(visible, (newVal) => {
	emit('update:visible', newVal);
});

// 发布渠道标签映射
const channelLabels = {
	official_website: '官方网站',
	government_platform: '政府采购平台',
	industry_platform: '行业平台',
	email: '邮件通知',
	sms: '短信通知',
};

// 获取发布渠道标签
function getPublishChannelLabels(): string[] {
	return props.announcementData.publishChannels.map(
		(channel: string) => channelLabels[channel as keyof typeof channelLabels] || channel
	);
}

// 格式化日期时间
function formatDateTime(date: Date | string): string {
	const d = new Date(date);
	return d.toLocaleString('zh-CN', {
		year: 'numeric',
		month: '2-digit',
		day: '2-digit',
		hour: '2-digit',
		minute: '2-digit',
	});
}

// 格式化日期
function formatDate(date: Date | string): string {
	const d = new Date(date);
	return d.toLocaleDateString('zh-CN', {
		year: 'numeric',
		month: '2-digit',
		day: '2-digit',
	});
}

// 格式化价格
function formatPrice(price: number): string {
	return `¥${price.toFixed(2)}`;
}

// 格式化节约金额
function formatSavings(row: WinnerInfo): string {
	const plannedPrice = 250; // 这里应该从实际数据获取
	const savings = plannedPrice - row.winnerPrice;
	return savings > 0 ? `¥${savings.toFixed(2)}` : '¥0.00';
}

// 格式化节约率
function formatSavingsRate(row: WinnerInfo): string {
	const plannedPrice = 250; // 这里应该从实际数据获取
	const savings = plannedPrice - row.winnerPrice;
	const rate = savings > 0 ? (savings / plannedPrice) * 100 : 0;
	return `${rate.toFixed(1)}%`;
}

// 处理关闭
function handleClose() {
	visible.value = false;
}

// 处理打印
function handlePrint() {
	window.print();
}

// 处理下载PDF
function handleDownload() {
	console.log('下载PDF');
	// 实现PDF下载逻辑
}
</script>

<style lang="scss" scoped>
.announcement-preview {
	font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
	line-height: 1.6;
	color: #333;

	.announcement-header {
		text-align: center;
		margin-bottom: 30px;
		padding-bottom: 20px;
		border-bottom: 2px solid #e5e7eb;

		.announcement-title {
			font-size: 24px;
			font-weight: 600;
			color: #1d2129;
			margin: 0 0 10px 0;
		}

		.announcement-meta {
			font-size: 14px;
			color: #6b7280;
		}
	}

	.announcement-body {
		.content-section {
			margin-bottom: 30px;

			.announcement-content {
				white-space: pre-wrap;
				font-family: inherit;
				font-size: 14px;
				line-height: 1.8;
				margin: 0;
			}
		}

		.winner-table-section,
		.objection-section,
		.contact-section,
		.publish-channels-section {
			margin-bottom: 30px;

			h4 {
				font-size: 16px;
				font-weight: 600;
				color: #1d2129;
				margin: 0 0 15px 0;
				padding-bottom: 8px;
				border-bottom: 1px solid #e5e7eb;
			}
		}

		.winner-table {
			width: 100%;
			border-collapse: collapse;
			font-size: 14px;

			th,
			td {
				border: 1px solid #d1d5db;
				padding: 8px 12px;
				text-align: left;
			}

			th {
				background-color: #f9fafb;
				font-weight: 600;
				text-align: center;
			}

			td {
				text-align: center;
			}

			.price-cell,
			.savings-cell {
				color: #0069ff;
				font-weight: 500;
			}

			.rate-cell {
				color: #52c41a;
				font-weight: 500;
			}
		}

		.contact-info {
			p {
				margin: 8px 0;
				font-size: 14px;
			}
		}

		.channels-list {
			display: flex;
			flex-wrap: wrap;
			gap: 8px;

			.channel-tag {
				margin: 0;
			}
		}
	}

	.announcement-footer {
		margin-top: 40px;
		padding-top: 20px;
		border-top: 1px solid #e5e7eb;

		.signature {
			text-align: right;
			font-size: 14px;

			p {
				margin: 5px 0;
			}
		}
	}
}

.dialog-footer {
	text-align: right;
}

// 打印样式
@media print {
	.announcement-preview {
		font-size: 12px;

		.announcement-header .announcement-title {
			font-size: 18px;
		}

		.winner-table {
			font-size: 11px;

			th,
			td {
				padding: 6px 8px;
			}
		}
	}
}
</style>
