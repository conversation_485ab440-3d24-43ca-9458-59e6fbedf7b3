<template>
	<div class="winner-announcement-container">
		<!-- 中标供应商 -->
		<div class="winner-suppliers-section">
			<div class="section-header">
				<h3>中标供应商</h3>
				<div class="header-actions">
					<el-button
						type="primary"
						size="small"
						@click="handleEditSuppliers"
					>
						编辑
					</el-button>
				</div>
			</div>

			<el-table
				:data="winnerSuppliers"
				style="width: 100%"
				border
				stripe
			>
				<el-table-column
					label="序号"
					type="index"
					width="60"
					align="center"
				/>
				<el-table-column
					label="标的编号"
					prop="bidItemCode"
					width="120"
					align="center"
				/>
				<el-table-column
					label="物料名称"
					prop="materialName"
					min-width="150"
				/>
				<el-table-column
					label="中标供应商"
					prop="supplierName"
					min-width="200"
				/>
				<el-table-column
					label="统一社会信用代码"
					prop="creditCode"
					width="180"
					align="center"
				/>
				<el-table-column
					label="中标金额"
					prop="awardAmount"
					width="120"
					align="center"
				>
					<template #default="{ row }">
						<span class="price-text">{{ formatPrice(row.awardAmount) }}</span>
					</template>
				</el-table-column>
				<el-table-column
					label="供应商地址"
					prop="supplierAddress"
					min-width="200"
				/>
			</el-table>
		</div>

		<!-- 公告要求 -->
		<div class="announcement-requirements-section">
			<div class="section-header">
				<h3>公告要求</h3>
				<div class="header-actions">
					<el-button
						type="primary"
						size="small"
						@click="handleEditRequirements"
					>
						编辑
					</el-button>
				</div>
			</div>

			<el-form
				:model="announcementRequirements"
				label-width="120px"
			>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="公告期限">
							<el-input
								v-model="announcementRequirements.duration"
								readonly
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="发布时间要求">
							<el-input
								v-model="announcementRequirements.publishTimeRequirement"
								readonly
							/>
						</el-form-item>
					</el-col>
				</el-row>

				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="公告范围">
							<el-input
								v-model="announcementRequirements.scope"
								readonly
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="法律依据">
							<el-input
								v-model="announcementRequirements.legalBasis"
								readonly
							/>
						</el-form-item>
					</el-col>
				</el-row>

				<el-form-item label="公告内容要求">
					<el-input
						v-model="announcementRequirements.contentRequirements"
						type="textarea"
						:rows="4"
						readonly
					/>
				</el-form-item>
			</el-form>
		</div>

		<!-- 公告详情 -->
		<div class="announcement-details-section">
			<div class="section-header">
				<h3>公告详情</h3>
				<div class="header-actions">
					<el-button
						type="primary"
						size="small"
						@click="handleEditDetails"
					>
						编辑
					</el-button>
					<el-button
						type="success"
						size="small"
						@click="handlePreview"
					>
						预览
					</el-button>
				</div>
			</div>

			<el-form
				:model="announcementDetails"
				label-width="120px"
			>
				<el-form-item label="公告标题">
					<el-input
						v-model="announcementDetails.title"
						placeholder="请输入公告标题"
						maxlength="100"
						show-word-limit
					/>
				</el-form-item>

				<el-form-item label="公告内容">
					<el-input
						v-model="announcementDetails.content"
						type="textarea"
						:rows="10"
						placeholder="请输入公告内容"
						maxlength="3000"
						show-word-limit
					/>
				</el-form-item>

				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="联系人">
							<el-input
								v-model="announcementDetails.contactPerson"
								placeholder="请输入联系人"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="联系电话">
							<el-input
								v-model="announcementDetails.contactPhone"
								placeholder="请输入联系电话"
							/>
						</el-form-item>
					</el-col>
				</el-row>

				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="联系邮箱">
							<el-input
								v-model="announcementDetails.contactEmail"
								placeholder="请输入联系邮箱"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="联系地址">
							<el-input
								v-model="announcementDetails.contactAddress"
								placeholder="请输入联系地址"
							/>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</div>

		<!-- 公告平台 -->
		<div class="announcement-platforms-section">
			<div class="section-header">
				<h3>公告平台</h3>
				<div class="header-actions">
					<el-button
						type="success"
						size="small"
						@click="handlePublishAnnouncement"
						:loading="publishing"
					>
						发布公告
					</el-button>
				</div>
			</div>

			<el-form label-width="120px">
				<el-form-item label="发布平台">
					<el-checkbox-group v-model="selectedPlatforms">
						<el-checkbox
							v-for="platform in availablePlatforms"
							:key="platform.value"
							:label="platform.value"
						>
							{{ platform.label }}
						</el-checkbox>
					</el-checkbox-group>
				</el-form-item>

				<el-form-item label="发布状态">
					<el-table
						:data="platformStatus"
						style="width: 100%"
						border
						size="small"
					>
						<el-table-column label="平台名称" prop="name" />
						<el-table-column label="发布状态" width="100" align="center">
							<template #default="{ row }">
								<el-tag
									:type="getPublishStatusType(row.status)"
									size="small"
								>
									{{ getPublishStatusText(row.status) }}
								</el-tag>
							</template>
						</el-table-column>
						<el-table-column label="发布时间" prop="publishTime" width="150" />
						<el-table-column label="访问链接" min-width="200">
							<template #default="{ row }">
								<el-link
									v-if="row.url"
									:href="row.url"
									target="_blank"
									type="primary"
								>
									{{ row.url }}
								</el-link>
								<span v-else class="text-gray-500">-</span>
							</template>
						</el-table-column>
					</el-table>
				</el-form-item>
			</el-form>
		</div>

		<!-- 预览弹窗 -->
		<AnnouncementPreviewDialog
			v-model:visible="previewDialogVisible"
			:announcement-data="announcementDetails"
			:winner-suppliers="winnerSuppliers"
		/>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import AnnouncementPreviewDialog from './AnnouncementPreviewDialog.vue';

// 预览弹窗状态
const previewDialogVisible = ref(false);

// 发布状态
const publishing = ref(false);

// 中标供应商数据
const winnerSuppliers = ref([
	{
		id: '1',
		bidItemCode: 'ASW12345',
		materialName: '特种合金01',
		supplierName: '上海中华控股股份有限公司',
		creditCode: '91310000123456789X',
		awardAmount: 235.50,
		supplierAddress: '上海市浦东新区张江高科技园区某某路123号',
	},
]);

// 公告要求
const announcementRequirements = reactive({
	duration: '不少于5个工作日',
	publishTimeRequirement: '中标结果确定后3个工作日内',
	scope: '政府采购网、采购人官网等指定媒体',
	legalBasis: '《政府采购法》第四十三条',
	contentRequirements: `公告内容应包括：
1. 采购项目名称、编号
2. 中标供应商名称、地址、中标金额
3. 主要中标标的的名称、规格型号、数量、单价、服务要求
4. 中标结果的确定依据
5. 采购人、采购代理机构名称、地址和联系方式`,
});

// 公告详情
const announcementDetails = reactive({
	title: 'XDMY-LYMG-20240810李原牧歌牧场原料采购项目中标公告',
	content: `根据《中华人民共和国政府采购法》等相关法律法规，现将XDMY-LYMG-20240810李原牧歌牧场原料采购项目的中标结果公告如下：

一、项目基本信息
项目名称：XDMY-LYMG-20240810李原牧歌牧场原料采购项目
项目编号：XDMY-LYMG-20240810
采购方式：公开询价
采购人：XDMY-LYMG-20240810李原牧歌牧场

二、中标结果
标的编号：ASW12345
物料名称：特种合金01
中标供应商：上海中华控股股份有限公司
供应商地址：上海市浦东新区张江高科技园区某某路123号
统一社会信用代码：91310000123456789X
中标金额：¥235.50

三、中标结果确定依据
本次采购严格按照招标文件规定的评标标准和方法进行评审，综合考虑技术、商务、服务等因素，确定上述供应商为中标供应商。

四、联系方式
详见下方联系信息。

特此公告。

采购人：XDMY-LYMG-20240810李原牧歌牧场
日期：${new Date().toLocaleDateString('zh-CN')}`,
	contactPerson: '王小二',
	contactPhone: '010-12345678',
	contactEmail: '<EMAIL>',
	contactAddress: '北京市朝阳区某某街道123号',
});

// 可用平台
const availablePlatforms = [
	{ label: '中国政府采购网', value: 'ccgp' },
	{ label: '省级政府采购网', value: 'provincial' },
	{ label: '采购人官网', value: 'company' },
	{ label: '行业媒体', value: 'industry' },
];

// 选中的平台
const selectedPlatforms = ref(['ccgp', 'company']);

// 平台发布状态
const platformStatus = ref([
	{
		name: '中国政府采购网',
		status: 'published',
		publishTime: '2025-01-31 16:00',
		url: 'https://www.ccgp.gov.cn/cggg/zygg/gkzb/12345',
	},
	{
		name: '采购人官网',
		status: 'pending',
		publishTime: '',
		url: '',
	},
]);

// 格式化价格
function formatPrice(price: number): string {
	return `¥${price.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
}

// 获取发布状态类型
function getPublishStatusType(status: string): string {
	const typeMap: Record<string, string> = {
		pending: 'warning',
		published: 'success',
		failed: 'danger',
	};
	return typeMap[status] || '';
}

// 获取发布状态文本
function getPublishStatusText(status: string): string {
	const textMap: Record<string, string> = {
		pending: '待发布',
		published: '已发布',
		failed: '发布失败',
	};
	return textMap[status] || '未知';
}

// 处理编辑供应商
function handleEditSuppliers() {
	ElMessage.info('编辑供应商功能开发中');
}

// 处理编辑要求
function handleEditRequirements() {
	ElMessage.info('编辑要求功能开发中');
}

// 处理编辑详情
function handleEditDetails() {
	ElMessage.info('编辑详情功能开发中');
}

// 处理预览
function handlePreview() {
	previewDialogVisible.value = true;
}

// 处理发布公告
async function handlePublishAnnouncement() {
	if (selectedPlatforms.value.length === 0) {
		ElMessage.warning('请选择发布平台');
		return;
	}

	publishing.value = true;
	try {
		// 模拟发布过程
		await new Promise(resolve => setTimeout(resolve, 2000));

		// 更新平台状态
		selectedPlatforms.value.forEach(platform => {
			const status = platformStatus.value.find(p => p.name === availablePlatforms.find(ap => ap.value === platform)?.label);
			if (status) {
				status.status = 'published';
				status.publishTime = new Date().toLocaleString();
				status.url = `https://example.com/${platform}/announcement/12345`;
			}
		});

		ElMessage.success('公告发布成功');
	} catch (error) {
		ElMessage.error('发布失败');
	} finally {
		publishing.value = false;
	}
}
</script>

<style lang="scss" scoped>
.winner-announcement-container {
	padding: 20px;
	background: #fff;
	border-radius: 6px;
}

.winner-suppliers-section,
.announcement-requirements-section,
.announcement-details-section,
.announcement-platforms-section {
	margin-bottom: 32px;

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16px;

		h3 {
			margin: 0;
			font-size: 16px;
			font-weight: 600;
			color: var(--Color-Text-text-color-primary, #1d2129);
		}

		.header-actions {
			display: flex;
			gap: 8px;
		}
	}
}

.price-text {
	font-weight: 500;
	color: var(--Color-Primary-color-primary, #0069ff);
}

.text-gray-500 {
	color: #6b7280;
}
</style>
