<template>
	<el-dialog
		v-model="visible"
		title="中标通知书预览"
		width="800px"
		:before-close="handleClose"
	>
		<div class="notification-preview">
			<!-- 通知书头部 -->
			<div class="notification-header">
				<h2 class="notification-title">{{ notificationData?.title }}</h2>
				<div class="notification-meta">
					<span>通知书编号：{{ notificationData?.notificationNumber }}</span>
					<span>日期：{{ formatDate(notificationData?.notificationDate) }}</span>
				</div>
			</div>

			<!-- 通知书内容 -->
			<div class="notification-body">
				<div class="content-section">
					<pre class="notification-content">{{ notificationData?.content }}</pre>
				</div>

				<!-- 收件人信息 -->
				<div class="recipient-info">
					<h4>收件人信息</h4>
					<el-descriptions :column="2" border size="small">
						<el-descriptions-item label="公司名称">{{ companyData?.companyName }}</el-descriptions-item>
						<el-descriptions-item label="统一社会信用代码">{{ companyData?.creditCode }}</el-descriptions-item>
						<el-descriptions-item label="联系人">{{ companyData?.contactPerson }}</el-descriptions-item>
						<el-descriptions-item label="联系电话">{{ companyData?.contactPhone }}</el-descriptions-item>
						<el-descriptions-item label="邮箱地址">{{ notificationData?.recipientEmail }}</el-descriptions-item>
						<el-descriptions-item label="收件地址">{{ notificationData?.recipientAddress }}</el-descriptions-item>
					</el-descriptions>
				</div>

				<!-- 发送方式 -->
				<div class="send-methods">
					<h4>发送方式</h4>
					<div class="methods-list">
						<el-tag
							v-for="method in notificationData?.sendMethods"
							:key="method"
							type="primary"
							size="small"
							class="method-tag"
						>
							{{ getSendMethodText(method) }}
						</el-tag>
					</div>
				</div>

				<!-- 附件信息 -->
				<div v-if="notificationData?.attachments?.length" class="attachments-info">
					<h4>附件信息</h4>
					<el-table
						:data="notificationData.attachments"
						style="width: 100%"
						size="small"
						border
					>
						<el-table-column label="文件名" prop="name" />
						<el-table-column label="文件大小" width="100">
							<template #default="{ row }">
								{{ formatFileSize(row.size || 0) }}
							</template>
						</el-table-column>
						<el-table-column label="文件类型" width="100">
							<template #default="{ row }">
								{{ getFileExtension(row.name) }}
							</template>
						</el-table-column>
					</el-table>
				</div>
			</div>

			<!-- 通知书尾部 -->
			<div class="notification-footer">
				<div class="signature">
					<p>采购人：XDMY-LYMG-20240810李原牧歌牧场</p>
					<p>{{ formatDate(notificationData?.notificationDate) }}</p>
				</div>
			</div>
		</div>

		<template #footer>
			<div class="dialog-footer">
				<el-button @click="handleClose">关闭</el-button>
				<el-button type="primary" @click="handlePrint">打印</el-button>
				<el-button type="success" @click="handleDownload">下载PDF</el-button>
				<el-button type="warning" @click="handleSend">发送通知书</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';

interface Props {
	visible: boolean;
	notificationData: any;
	companyData: any;
}

interface Emits {
	(e: 'update:visible', value: boolean): void;
	(e: 'send'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(props.visible);

// 监听visible变化
watch(() => props.visible, (newVal) => {
	visible.value = newVal;
});

watch(visible, (newVal) => {
	emit('update:visible', newVal);
});

// 获取发送方式文本
function getSendMethodText(method: string): string {
	const textMap: Record<string, string> = {
		email: '邮件发送',
		express: '快递邮寄',
		system: '系统通知',
	};
	return textMap[method] || '未知方式';
}

// 格式化日期
function formatDate(date: Date | string): string {
	if (!date) return '';
	const d = new Date(date);
	return d.toLocaleDateString('zh-CN', {
		year: 'numeric',
		month: '2-digit',
		day: '2-digit',
	});
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
	if (bytes === 0) return '0 B';
	const k = 1024;
	const sizes = ['B', 'KB', 'MB', 'GB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 获取文件扩展名
function getFileExtension(filename: string): string {
	return filename.split('.').pop()?.toUpperCase() || '';
}

// 处理关闭
function handleClose() {
	visible.value = false;
}

// 处理打印
function handlePrint() {
	window.print();
}

// 处理下载PDF
function handleDownload() {
	ElMessage.info('PDF下载功能开发中');
}

// 处理发送
function handleSend() {
	emit('send');
	handleClose();
}
</script>

<style lang="scss" scoped>
.notification-preview {
	font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
	line-height: 1.6;
	color: #333;

	.notification-header {
		text-align: center;
		margin-bottom: 30px;
		padding-bottom: 20px;
		border-bottom: 2px solid #e5e7eb;

		.notification-title {
			font-size: 24px;
			font-weight: 600;
			color: #1d2129;
			margin: 0 0 10px 0;
		}

		.notification-meta {
			display: flex;
			justify-content: space-between;
			font-size: 14px;
			color: #6b7280;
		}
	}

	.notification-body {
		.content-section {
			margin-bottom: 30px;

			.notification-content {
				white-space: pre-wrap;
				font-family: inherit;
				font-size: 14px;
				line-height: 1.8;
				margin: 0;
				padding: 20px;
				background: #f9fafb;
				border-radius: 6px;
				border: 1px solid #e5e7eb;
			}
		}

		.recipient-info,
		.send-methods,
		.attachments-info {
			margin-bottom: 24px;

			h4 {
				font-size: 16px;
				font-weight: 600;
				color: #1d2129;
				margin: 0 0 12px 0;
				padding-bottom: 8px;
				border-bottom: 1px solid #e5e7eb;
			}
		}

		.send-methods {
			.methods-list {
				display: flex;
				flex-wrap: wrap;
				gap: 8px;

				.method-tag {
					margin: 0;
				}
			}
		}
	}

	.notification-footer {
		margin-top: 40px;
		padding-top: 20px;
		border-top: 1px solid #e5e7eb;

		.signature {
			text-align: right;
			font-size: 14px;

			p {
				margin: 5px 0;
			}
		}
	}
}

.dialog-footer {
	text-align: right;
}

// 打印样式
@media print {
	.notification-preview {
		font-size: 12px;

		.notification-header .notification-title {
			font-size: 18px;
		}

		.notification-body .content-section .notification-content {
			font-size: 11px;
		}
	}
}
</style>
