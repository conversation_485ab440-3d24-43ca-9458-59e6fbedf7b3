<template>
	<div class="winner-notification-container">
		<!-- 中标公司 -->
		<div class="winner-companies-section">
			<div class="section-header">
				<h3>中标公司</h3>
				<div class="header-actions">
					<el-button
						type="primary"
						size="small"
						@click="handleSelectCompany"
					>
						选择公司
					</el-button>
				</div>
			</div>

			<el-table
				:data="winnerCompanies"
				style="width: 100%"
				border
				stripe
				@row-click="handleCompanyClick"
				highlight-current-row
			>
				<el-table-column
					label="序号"
					type="index"
					width="60"
					align="center"
				/>
				<el-table-column
					label="标的编号"
					prop="bidItemCode"
					width="120"
					align="center"
				/>
				<el-table-column
					label="物料名称"
					prop="materialName"
					min-width="150"
				/>
				<el-table-column
					label="中标供应商"
					prop="companyName"
					min-width="200"
				/>
				<el-table-column
					label="统一社会信用代码"
					prop="creditCode"
					width="180"
					align="center"
				/>
				<el-table-column
					label="中标金额"
					prop="awardAmount"
					width="120"
					align="center"
				>
					<template #default="{ row }">
						<span class="price-text">{{ formatPrice(row.awardAmount) }}</span>
					</template>
				</el-table-column>
				<el-table-column
					label="通知状态"
					prop="notificationStatus"
					width="100"
					align="center"
				>
					<template #default="{ row }">
						<el-tag
							:type="getNotificationStatusType(row.notificationStatus)"
							size="small"
						>
							{{ getNotificationStatusText(row.notificationStatus) }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column
					label="操作"
					width="150"
					align="center"
					fixed="right"
				>
					<template #default="{ row }">
						<el-button
							type="primary"
							size="small"
							@click="handleEditNotification(row)"
						>
							编辑通知书
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<!-- 中标通知书编辑 -->
		<div v-if="selectedCompany" class="notification-editor-section">
			<div class="section-header">
				<h3>中标通知书 - {{ selectedCompany.companyName }}</h3>
				<div class="header-actions">
					<el-button
						type="primary"
						size="small"
						@click="handlePreview"
					>
						预览
					</el-button>
					<el-button
						type="success"
						size="small"
						@click="handleSave"
						:loading="saving"
					>
						保存
					</el-button>
					<el-button
						type="warning"
						size="small"
						@click="handleSend"
						:loading="sending"
					>
						发送通知书
					</el-button>
				</div>
			</div>

			<div class="notification-form">
				<el-form
					:model="notificationForm"
					label-width="120px"
				>
					<el-row :gutter="20">
						<el-col :span="12">
							<el-form-item label="通知书编号">
								<el-input
									v-model="notificationForm.notificationNumber"
									placeholder="请输入通知书编号"
								/>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="通知书日期">
								<el-date-picker
									v-model="notificationForm.notificationDate"
									type="date"
									placeholder="选择通知书日期"
									style="width: 100%"
								/>
							</el-form-item>
						</el-col>
					</el-row>

					<el-form-item label="通知书标题">
						<el-input
							v-model="notificationForm.title"
							placeholder="请输入通知书标题"
							maxlength="100"
							show-word-limit
						/>
					</el-form-item>

					<el-form-item label="通知书内容">
						<div class="editor-toolbar">
							<el-button-group size="small">
								<el-button @click="insertTemplate('header')">插入抬头</el-button>
								<el-button @click="insertTemplate('project')">插入项目信息</el-button>
								<el-button @click="insertTemplate('winner')">插入中标信息</el-button>
								<el-button @click="insertTemplate('requirements')">插入后续要求</el-button>
								<el-button @click="insertTemplate('contact')">插入联系方式</el-button>
								<el-button @click="insertTemplate('footer')">插入结尾</el-button>
							</el-button-group>
						</div>
						<el-input
							v-model="notificationForm.content"
							type="textarea"
							:rows="20"
							placeholder="请输入通知书内容"
							maxlength="5000"
							show-word-limit
						/>
					</el-form-item>

					<el-row :gutter="20">
						<el-col :span="12">
							<el-form-item label="发送方式">
								<el-checkbox-group v-model="notificationForm.sendMethods">
									<el-checkbox label="email">邮件</el-checkbox>
									<el-checkbox label="express">快递</el-checkbox>
									<el-checkbox label="system">系统通知</el-checkbox>
								</el-checkbox-group>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="收件人邮箱">
								<el-input
									v-model="notificationForm.recipientEmail"
									placeholder="请输入收件人邮箱"
								/>
							</el-form-item>
						</el-col>
					</el-row>

					<el-row :gutter="20">
						<el-col :span="12">
							<el-form-item label="收件人地址">
								<el-input
									v-model="notificationForm.recipientAddress"
									placeholder="请输入收件人地址"
								/>
							</el-form-item>
						</el-col>
						<el-col :span="12">
							<el-form-item label="联系电话">
								<el-input
									v-model="notificationForm.contactPhone"
									placeholder="请输入联系电话"
								/>
							</el-form-item>
						</el-col>
					</el-row>

					<el-form-item label="附件">
						<el-upload
							class="upload-demo"
							:auto-upload="false"
							:on-change="handleFileChange"
							:file-list="notificationForm.attachments"
							multiple
						>
							<el-button type="primary">选择附件</el-button>
							<template #tip>
								<div class="el-upload__tip">
									支持多个文件上传，单个文件不超过10MB
								</div>
							</template>
						</el-upload>
					</el-form-item>
				</el-form>
			</div>
		</div>

		<!-- 发送记录 -->
		<div class="send-records-section">
			<div class="section-header">
				<h3>发送记录</h3>
			</div>

			<el-table
				:data="sendRecords"
				style="width: 100%"
				border
				stripe
			>
				<el-table-column
					label="序号"
					type="index"
					width="60"
					align="center"
				/>
				<el-table-column
					label="收件公司"
					prop="companyName"
					min-width="200"
				/>
				<el-table-column
					label="通知书编号"
					prop="notificationNumber"
					width="150"
					align="center"
				/>
				<el-table-column
					label="发送方式"
					prop="sendMethod"
					width="100"
					align="center"
				>
					<template #default="{ row }">
						<el-tag size="small">{{ getSendMethodText(row.sendMethod) }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column
					label="发送时间"
					prop="sendTime"
					width="150"
					align="center"
				/>
				<el-table-column
					label="发送状态"
					prop="sendStatus"
					width="100"
					align="center"
				>
					<template #default="{ row }">
						<el-tag
							:type="getSendStatusType(row.sendStatus)"
							size="small"
						>
							{{ getSendStatusText(row.sendStatus) }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column
					label="签收状态"
					prop="receiptStatus"
					width="100"
					align="center"
				>
					<template #default="{ row }">
						<el-tag
							:type="getReceiptStatusType(row.receiptStatus)"
							size="small"
						>
							{{ getReceiptStatusText(row.receiptStatus) }}
						</el-tag>
					</template>
				</el-table-column>
				<el-table-column
					label="操作"
					width="150"
					align="center"
					fixed="right"
				>
					<template #default="{ row }">
						<el-button
							type="primary"
							size="small"
							@click="handleViewRecord(row)"
						>
							查看
						</el-button>
						<el-button
							type="success"
							size="small"
							@click="handleResend(row)"
						>
							重发
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<!-- 预览弹窗 -->
		<NotificationPreviewDialog
			v-model:visible="previewDialogVisible"
			:notification-data="notificationForm"
			:company-data="selectedCompany"
		/>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { ElMessage } from 'element-plus';
import NotificationPreviewDialog from './NotificationPreviewDialog.vue';

// 状态
const saving = ref(false);
const sending = ref(false);
const previewDialogVisible = ref(false);

// 选中的公司
const selectedCompany = ref(null);

// 中标公司数据
const winnerCompanies = ref([
	{
		id: '1',
		bidItemCode: 'ASW12345',
		materialName: '特种合金01',
		companyName: '上海中华控股股份有限公司',
		creditCode: '91310000123456789X',
		awardAmount: 235.50,
		notificationStatus: 'pending', // pending, sent, confirmed
		contactPerson: '张经理',
		contactPhone: '021-12345678',
		contactEmail: '<EMAIL>',
		address: '上海市浦东新区张江高科技园区某某路123号',
	},
]);

// 通知书表单
const notificationForm = reactive({
	notificationNumber: '',
	notificationDate: new Date(),
	title: '',
	content: '',
	sendMethods: ['email'],
	recipientEmail: '',
	recipientAddress: '',
	contactPhone: '',
	attachments: [],
});

// 发送记录
const sendRecords = ref([
	{
		id: '1',
		companyName: '上海中华控股股份有限公司',
		notificationNumber: 'ZB-2025-001',
		sendMethod: 'email',
		sendTime: '2025-01-31 16:30',
		sendStatus: 'sent',
		receiptStatus: 'confirmed',
	},
]);

// 监听选中公司变化
watch(selectedCompany, (newCompany) => {
	if (newCompany) {
		initNotificationForm(newCompany);
	}
}, { immediate: true });

// 初始化通知书表单
function initNotificationForm(company: any) {
	notificationForm.notificationNumber = `ZB-${new Date().getFullYear()}-${String(Date.now()).slice(-3)}`;
	notificationForm.title = `${company.companyName}中标通知书`;
	notificationForm.recipientEmail = company.contactEmail;
	notificationForm.recipientAddress = company.address;
	notificationForm.contactPhone = company.contactPhone;
	notificationForm.content = generateNotificationContent(company);
}

// 生成通知书内容
function generateNotificationContent(company: any): string {
	return `${company.companyName}：

恭喜您在"XDMY-LYMG-20240810李原牧歌牧场原料采购项目"中成功中标！

一、项目基本信息
项目名称：XDMY-LYMG-20240810李原牧歌牧场原料采购项目
项目编号：XDMY-LYMG-20240810
采购方式：公开询价

二、中标信息
中标标的：${company.materialName}
中标金额：¥${company.awardAmount}
标的编号：${company.bidItemCode}

三、后续要求
1. 请于收到本通知后3个工作日内与采购人联系，确认中标事宜；
2. 请按照招标文件要求，在规定时间内签订采购合同；
3. 请严格按照投标承诺履行合同义务；
4. 如有疑问，请及时与我们联系。

四、联系方式
联系人：王小二
联系电话：010-12345678
联系邮箱：<EMAIL>
联系地址：北京市朝阳区某某街道123号

此致
敬礼！

采购人：XDMY-LYMG-20240810李原牧歌牧场
日期：${new Date().toLocaleDateString('zh-CN')}`;
}

// 格式化价格
function formatPrice(price: number): string {
	return `¥${price.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
}

// 获取通知状态类型
function getNotificationStatusType(status: string): string {
	const typeMap: Record<string, string> = {
		pending: 'warning',
		sent: 'primary',
		confirmed: 'success',
	};
	return typeMap[status] || '';
}

// 获取通知状态文本
function getNotificationStatusText(status: string): string {
	const textMap: Record<string, string> = {
		pending: '待发送',
		sent: '已发送',
		confirmed: '已确认',
	};
	return textMap[status] || '未知';
}

// 获取发送方式文本
function getSendMethodText(method: string): string {
	const textMap: Record<string, string> = {
		email: '邮件',
		express: '快递',
		system: '系统',
	};
	return textMap[method] || '未知';
}

// 获取发送状态类型
function getSendStatusType(status: string): string {
	const typeMap: Record<string, string> = {
		pending: 'info',
		sent: 'success',
		failed: 'danger',
	};
	return typeMap[status] || '';
}

// 获取发送状态文本
function getSendStatusText(status: string): string {
	const textMap: Record<string, string> = {
		pending: '待发送',
		sent: '已发送',
		failed: '发送失败',
	};
	return textMap[status] || '未知';
}

// 获取签收状态类型
function getReceiptStatusType(status: string): string {
	const typeMap: Record<string, string> = {
		pending: 'warning',
		confirmed: 'success',
		rejected: 'danger',
	};
	return typeMap[status] || '';
}

// 获取签收状态文本
function getReceiptStatusText(status: string): string {
	const textMap: Record<string, string> = {
		pending: '待签收',
		confirmed: '已签收',
		rejected: '已拒收',
	};
	return textMap[status] || '未知';
}

// 处理选择公司
function handleSelectCompany() {
	ElMessage.info('选择公司功能开发中');
}

// 处理公司点击
function handleCompanyClick(row: any) {
	selectedCompany.value = row;
}

// 处理编辑通知
function handleEditNotification(company: any) {
	selectedCompany.value = company;
}

// 插入模板
function insertTemplate(type: string) {
	const templates: Record<string, string> = {
		header: `${selectedCompany.value?.companyName}：

恭喜您在"XDMY-LYMG-20240810李原牧歌牧场原料采购项目"中成功中标！

`,
		project: `一、项目基本信息
项目名称：XDMY-LYMG-20240810李原牧歌牧场原料采购项目
项目编号：XDMY-LYMG-20240810
采购方式：公开询价

`,
		winner: `二、中标信息
中标标的：${selectedCompany.value?.materialName}
中标金额：¥${selectedCompany.value?.awardAmount}
标的编号：${selectedCompany.value?.bidItemCode}

`,
		requirements: `三、后续要求
1. 请于收到本通知后3个工作日内与采购人联系，确认中标事宜；
2. 请按照招标文件要求，在规定时间内签订采购合同；
3. 请严格按照投标承诺履行合同义务；
4. 如有疑问，请及时与我们联系。

`,
		contact: `四、联系方式
联系人：王小二
联系电话：010-12345678
联系邮箱：<EMAIL>
联系地址：北京市朝阳区某某街道123号

`,
		footer: `此致
敬礼！

采购人：XDMY-LYMG-20240810李原牧歌牧场
日期：${new Date().toLocaleDateString('zh-CN')}`,
	};

	if (templates[type]) {
		notificationForm.content += templates[type];
	}
}

// 处理文件变化
function handleFileChange(file: any, fileList: any[]) {
	notificationForm.attachments = fileList;
}

// 处理预览
function handlePreview() {
	previewDialogVisible.value = true;
}

// 处理保存
async function handleSave() {
	saving.value = true;
	try {
		// 模拟保存
		await new Promise(resolve => setTimeout(resolve, 1000));
		ElMessage.success('保存成功');
	} catch (error) {
		ElMessage.error('保存失败');
	} finally {
		saving.value = false;
	}
}

// 处理发送
async function handleSend() {
	sending.value = true;
	try {
		// 模拟发送
		await new Promise(resolve => setTimeout(resolve, 2000));
		
		// 更新状态
		if (selectedCompany.value) {
			selectedCompany.value.notificationStatus = 'sent';
		}
		
		// 添加发送记录
		sendRecords.value.unshift({
			id: Date.now().toString(),
			companyName: selectedCompany.value?.companyName,
			notificationNumber: notificationForm.notificationNumber,
			sendMethod: notificationForm.sendMethods[0],
			sendTime: new Date().toLocaleString(),
			sendStatus: 'sent',
			receiptStatus: 'pending',
		});
		
		ElMessage.success('通知书发送成功');
	} catch (error) {
		ElMessage.error('发送失败');
	} finally {
		sending.value = false;
	}
}

// 处理查看记录
function handleViewRecord(record: any) {
	ElMessage.info('查看记录功能开发中');
}

// 处理重发
function handleResend(record: any) {
	ElMessage.info('重发功能开发中');
}
</script>

<style lang="scss" scoped>
.winner-notification-container {
	padding: 20px;
	background: #fff;
	border-radius: 6px;
}

.winner-companies-section,
.notification-editor-section,
.send-records-section {
	margin-bottom: 32px;
	
	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16px;
		
		h3 {
			margin: 0;
			font-size: 16px;
			font-weight: 600;
			color: var(--Color-Text-text-color-primary, #1d2129);
		}
		
		.header-actions {
			display: flex;
			gap: 8px;
		}
	}
}

.notification-form {
	.editor-toolbar {
		margin-bottom: 8px;
	}
}

.price-text {
	font-weight: 500;
	color: var(--Color-Primary-color-primary, #0069ff);
}

:deep(.el-upload__tip) {
	margin-top: 8px;
	font-size: 12px;
	color: #6b7280;
}
</style>
