<template>
	<div class="winner-publicity-container">
		<!-- 公示公司信息 -->
		<div class="publicity-company-section">
			<div class="section-header">
				<h3>公示公司</h3>
				<div class="header-actions">
					<el-button
						type="primary"
						size="small"
						@click="handleEditCompany"
					>
						编辑
					</el-button>
				</div>
			</div>

			<el-table
				:data="winnerCompanies"
				style="width: 100%"
				border
				stripe
			>
				<el-table-column
					label="序号"
					type="index"
					width="60"
					align="center"
				/>
				<el-table-column
					label="标的编号"
					prop="bidItemCode"
					width="120"
					align="center"
				/>
				<el-table-column
					label="物料名称"
					prop="materialName"
					min-width="150"
				/>
				<el-table-column
					label="中标供应商"
					prop="companyName"
					min-width="200"
				/>
				<el-table-column
					label="统一社会信用代码"
					prop="creditCode"
					width="180"
					align="center"
				/>
				<el-table-column
					label="中标金额"
					prop="awardAmount"
					width="120"
					align="center"
				>
					<template #default="{ row }">
						<span class="price-text">{{ formatPrice(row.awardAmount) }}</span>
					</template>
				</el-table-column>
				<el-table-column
					label="联系人"
					prop="contactPerson"
					width="100"
					align="center"
				/>
				<el-table-column
					label="联系电话"
					prop="contactPhone"
					width="130"
					align="center"
				/>
			</el-table>
		</div>

		<!-- 公示要求 -->
		<div class="publicity-requirements-section">
			<div class="section-header">
				<h3>公示要求</h3>
				<div class="header-actions">
					<el-button
						type="primary"
						size="small"
						@click="handleEditRequirements"
					>
						编辑
					</el-button>
				</div>
			</div>

			<el-form
				:model="publicityRequirements"
				label-width="120px"
			>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="公示期限">
							<el-input
								v-model="publicityRequirements.duration"
								readonly
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="公示开始时间">
							<el-date-picker
								v-model="publicityRequirements.startTime"
								type="datetime"
								placeholder="选择开始时间"
								style="width: 100%"
								readonly
							/>
						</el-form-item>
					</el-col>
				</el-row>

				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="公示结束时间">
							<el-date-picker
								v-model="publicityRequirements.endTime"
								type="datetime"
								placeholder="选择结束时间"
								style="width: 100%"
								readonly
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="异议受理方式">
							<el-input
								v-model="publicityRequirements.objectionMethod"
								readonly
							/>
						</el-form-item>
					</el-col>
				</el-row>

				<el-form-item label="公示内容要求">
					<el-input
						v-model="publicityRequirements.contentRequirements"
						type="textarea"
						:rows="4"
						readonly
					/>
				</el-form-item>
			</el-form>
		</div>

		<!-- 公示详情 -->
		<div class="publicity-details-section">
			<div class="section-header">
				<h3>公示详情</h3>
				<div class="header-actions">
					<el-button
						type="primary"
						size="small"
						@click="handleEditDetails"
					>
						编辑
					</el-button>
					<el-button
						type="success"
						size="small"
						@click="handlePreview"
					>
						预览
					</el-button>
				</div>
			</div>

			<el-form
				:model="publicityDetails"
				label-width="120px"
			>
				<el-form-item label="公示标题">
					<el-input
						v-model="publicityDetails.title"
						placeholder="请输入公示标题"
						maxlength="100"
						show-word-limit
					/>
				</el-form-item>

				<el-form-item label="公示内容">
					<el-input
						v-model="publicityDetails.content"
						type="textarea"
						:rows="8"
						placeholder="请输入公示内容"
						maxlength="2000"
						show-word-limit
					/>
				</el-form-item>

				<el-form-item label="联系方式">
					<el-row :gutter="20">
						<el-col :span="12">
							<el-input
								v-model="publicityDetails.contactPerson"
								placeholder="联系人"
							/>
						</el-col>
						<el-col :span="12">
							<el-input
								v-model="publicityDetails.contactPhone"
								placeholder="联系电话"
							/>
						</el-col>
					</el-row>
				</el-form-item>

				<el-form-item label="联系邮箱">
					<el-input
						v-model="publicityDetails.contactEmail"
						placeholder="请输入联系邮箱"
					/>
				</el-form-item>
			</el-form>
		</div>

		<!-- 公示附件 -->
		<div class="publicity-attachments-section">
			<div class="section-header">
				<h3>公示附件</h3>
				<div class="header-actions">
					<el-button
						type="primary"
						size="small"
						@click="handleUploadAttachment"
					>
						上传附件
					</el-button>
				</div>
			</div>

			<el-table
				:data="publicityAttachments"
				style="width: 100%"
				border
				stripe
			>
				<el-table-column
					label="序号"
					type="index"
					width="60"
					align="center"
				/>
				<el-table-column
					label="附件名称"
					prop="name"
					min-width="200"
				/>
				<el-table-column
					label="附件类型"
					prop="type"
					width="120"
					align="center"
				>
					<template #default="{ row }">
						<el-tag size="small">{{ getAttachmentTypeText(row.type) }}</el-tag>
					</template>
				</el-table-column>
				<el-table-column
					label="文件大小"
					prop="size"
					width="100"
					align="center"
				>
					<template #default="{ row }">
						{{ formatFileSize(row.size) }}
					</template>
				</el-table-column>
				<el-table-column
					label="上传时间"
					prop="uploadTime"
					width="150"
					align="center"
				/>
				<el-table-column
					label="操作"
					width="150"
					align="center"
					fixed="right"
				>
					<template #default="{ row }">
						<el-button
							type="primary"
							size="small"
							@click="handleDownload(row)"
						>
							下载
						</el-button>
						<el-button
							type="danger"
							size="small"
							@click="handleDeleteAttachment(row)"
						>
							删除
						</el-button>
					</template>
				</el-table-column>
			</el-table>
		</div>

		<!-- 公示平台 -->
		<div class="publicity-platforms-section">
			<div class="section-header">
				<h3>公示平台</h3>
				<div class="header-actions">
					<el-button
						type="success"
						size="small"
						@click="handlePublish"
						:loading="publishing"
					>
						发布公示
					</el-button>
				</div>
			</div>

			<el-form label-width="120px">
				<el-form-item label="发布平台">
					<el-checkbox-group v-model="selectedPlatforms">
						<el-checkbox
							v-for="platform in availablePlatforms"
							:key="platform.value"
							:label="platform.value"
						>
							{{ platform.label }}
						</el-checkbox>
					</el-checkbox-group>
				</el-form-item>

				<el-form-item label="发布状态">
					<el-table
						:data="platformStatus"
						style="width: 100%"
						border
						size="small"
					>
						<el-table-column label="平台名称" prop="name" />
						<el-table-column label="发布状态" width="100" align="center">
							<template #default="{ row }">
								<el-tag
									:type="getPublishStatusType(row.status)"
									size="small"
								>
									{{ getPublishStatusText(row.status) }}
								</el-tag>
							</template>
						</el-table-column>
						<el-table-column label="发布时间" prop="publishTime" width="150" />
						<el-table-column label="访问链接" min-width="200">
							<template #default="{ row }">
								<el-link
									v-if="row.url"
									:href="row.url"
									target="_blank"
									type="primary"
								>
									{{ row.url }}
								</el-link>
								<span v-else class="text-gray-500">-</span>
							</template>
						</el-table-column>
					</el-table>
				</el-form-item>
			</el-form>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

// 发布状态
const publishing = ref(false);

// 中标公司数据
const winnerCompanies = ref([
	{
		id: '1',
		bidItemCode: 'ASW12345',
		materialName: '特种合金01',
		companyName: '上海中华控股股份有限公司',
		creditCode: '91310000123456789X',
		awardAmount: 235.50,
		contactPerson: '张经理',
		contactPhone: '021-12345678',
	},
]);

// 公示要求
const publicityRequirements = reactive({
	duration: '7个工作日',
	startTime: new Date(),
	endTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
	objectionMethod: '书面形式向采购人提出',
	contentRequirements: `公示内容应包括：
1. 中标供应商名称、地址、联系方式
2. 中标金额和主要技术参数
3. 评标委员会成员名单
4. 中标结果的确定依据
5. 异议受理联系人和联系方式`,
});

// 公示详情
const publicityDetails = reactive({
	title: 'XDMY-LYMG-20240810李原牧歌牧场原料采购项目中标公示',
	content: `根据《中华人民共和国政府采购法》等相关法律法规，现将XDMY-LYMG-20240810李原牧歌牧场原料采购项目的中标结果进行公示：

一、项目基本信息
项目名称：XDMY-LYMG-20240810李原牧歌牧场原料采购项目
项目编号：XDMY-LYMG-20240810
采购方式：公开询价

二、中标结果
详见中标供应商信息表。

三、公示期
自本公示发布之日起7个工作日内，任何供应商或者其他利害关系人对中标结果有异议的，可以向采购人提出质疑。

四、质疑方式
质疑应当以书面形式提出，并提供相关证明材料。

特此公示。`,
	contactPerson: '王小二',
	contactPhone: '010-12345678',
	contactEmail: '<EMAIL>',
});

// 公示附件
const publicityAttachments = ref([
	{
		id: '1',
		name: '中标结果公示.pdf',
		type: 'publicity',
		size: 1024 * 1024 * 1.5,
		uploadTime: '2025-01-31 14:00',
		url: '/documents/winner-publicity.pdf',
	},
]);

// 可用平台
const availablePlatforms = [
	{ label: '政府采购网', value: 'government' },
	{ label: '公司官网', value: 'company' },
	{ label: '行业平台', value: 'industry' },
	{ label: '第三方平台', value: 'third_party' },
];

// 选中的平台
const selectedPlatforms = ref(['government', 'company']);

// 平台发布状态
const platformStatus = ref([
	{
		name: '政府采购网',
		status: 'published',
		publishTime: '2025-01-31 15:00',
		url: 'https://www.ccgp.gov.cn/notice/12345',
	},
	{
		name: '公司官网',
		status: 'pending',
		publishTime: '',
		url: '',
	},
]);

// 格式化价格
function formatPrice(price: number): string {
	return `¥${price.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
	if (bytes === 0) return '0 B';
	const k = 1024;
	const sizes = ['B', 'KB', 'MB', 'GB'];
	const i = Math.floor(Math.log(bytes) / Math.log(k));
	return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 获取附件类型文本
function getAttachmentTypeText(type: string): string {
	const typeMap: Record<string, string> = {
		publicity: '公示文件',
		evaluation: '评标报告',
		decision: '定标决定',
		other: '其他',
	};
	return typeMap[type] || '未知类型';
}

// 获取发布状态类型
function getPublishStatusType(status: string): string {
	const typeMap: Record<string, string> = {
		pending: 'warning',
		published: 'success',
		failed: 'danger',
	};
	return typeMap[status] || '';
}

// 获取发布状态文本
function getPublishStatusText(status: string): string {
	const textMap: Record<string, string> = {
		pending: '待发布',
		published: '已发布',
		failed: '发布失败',
	};
	return textMap[status] || '未知';
}

// 处理编辑公司
function handleEditCompany() {
	ElMessage.info('编辑公司功能开发中');
}

// 处理编辑要求
function handleEditRequirements() {
	ElMessage.info('编辑要求功能开发中');
}

// 处理编辑详情
function handleEditDetails() {
	ElMessage.info('编辑详情功能开发中');
}

// 处理预览
function handlePreview() {
	ElMessage.info('预览功能开发中');
}

// 处理上传附件
function handleUploadAttachment() {
	ElMessage.info('上传附件功能开发中');
}

// 处理下载
function handleDownload(attachment: any) {
	window.open(attachment.url, '_blank');
}

// 处理删除附件
async function handleDeleteAttachment(attachment: any) {
	try {
		await ElMessageBox.confirm(
			`确定要删除附件"${attachment.name}"吗？`,
			'确认删除',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			}
		);

		const index = publicityAttachments.value.findIndex(item => item.id === attachment.id);
		if (index > -1) {
			publicityAttachments.value.splice(index, 1);
			ElMessage.success('删除成功');
		}
	} catch {
		// 用户取消
	}
}

// 处理发布
async function handlePublish() {
	if (selectedPlatforms.value.length === 0) {
		ElMessage.warning('请选择发布平台');
		return;
	}

	publishing.value = true;
	try {
		// 模拟发布过程
		await new Promise(resolve => setTimeout(resolve, 2000));

		// 更新平台状态
		selectedPlatforms.value.forEach(platform => {
			const status = platformStatus.value.find(p => p.name === availablePlatforms.find(ap => ap.value === platform)?.label);
			if (status) {
				status.status = 'published';
				status.publishTime = new Date().toLocaleString();
				status.url = `https://example.com/${platform}/12345`;
			}
		});

		ElMessage.success('公示发布成功');
	} catch (error) {
		ElMessage.error('发布失败');
	} finally {
		publishing.value = false;
	}
}
</script>

<style lang="scss" scoped>
.winner-publicity-container {
	padding: 20px;
	background: #fff;
	border-radius: 6px;
}

.publicity-company-section,
.publicity-requirements-section,
.publicity-details-section,
.publicity-attachments-section,
.publicity-platforms-section {
	margin-bottom: 32px;

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16px;

		h3 {
			margin: 0;
			font-size: 16px;
			font-weight: 600;
			color: var(--Color-Text-text-color-primary, #1d2129);
		}

		.header-actions {
			display: flex;
			gap: 8px;
		}
	}
}

.price-text {
	font-weight: 500;
	color: var(--Color-Primary-color-primary, #0069ff);
}

.text-gray-500 {
	color: #6b7280;
}
</style>
