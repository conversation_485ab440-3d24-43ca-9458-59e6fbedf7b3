<template>
	<el-drawer
		v-model="visible"
		:title="'审核'"
		size="60vw"
		:with-header="true"
		custom-class="custom-drawer"
	>
		<div>
			<!-- 企业基础资料 -->
			<div class="form-section">
				<div class="label-header">企业基础资料</div>
				<el-descriptions :column="2">
					<el-descriptions-item label="企业名称">{{ detail?.company.name }}</el-descriptions-item>
					<el-descriptions-item label="统一社会信用代码">{{ detail?.company.creditCode }}</el-descriptions-item>
					<el-descriptions-item label="成立日期">{{ detail?.company.registerDate }}</el-descriptions-item>
					<el-descriptions-item label="注册资本">{{ detail?.company.registerCapital }}</el-descriptions-item>
					<el-descriptions-item label="法人代表">{{ detail?.company.legalPerson }}</el-descriptions-item>
					<el-descriptions-item label="详细地址">{{ detail?.company.address }}</el-descriptions-item>
				</el-descriptions>
			</div>
			<!-- 联系人信息 -->
			<div class="form-section">
				<div class="label-header">联系人信息</div>
				<el-descriptions :column="2">
					<el-descriptions-item label="联系人">{{ detail?.contact.name }}</el-descriptions-item>
					<el-descriptions-item label="联系电话">{{ detail?.contact.phone }}</el-descriptions-item>
					<el-descriptions-item label="联系邮箱">{{ detail?.contact.email }}</el-descriptions-item>
				</el-descriptions>
			</div>
			<!-- 预审材料 -->
			<div class="form-section">
				<div class="label-header">预审材料</div>
				<el-descriptions :column="2">
					<el-descriptions-item label="1号证件">
						<el-link
							type="primary"
							:href="detail?.preAudit.idCard"
							target="_blank"
							>查看文件</el-link
						>
					</el-descriptions-item>
					<el-descriptions-item label="业绩证件">
						<el-link
							type="primary"
							:href="detail?.preAudit.businessLicense"
							target="_blank"
							>查看文件</el-link
						>
					</el-descriptions-item>
					<el-descriptions-item label="其它证件">
						<el-link
							type="primary"
							:href="detail?.preAudit.other"
							target="_blank"
							>查看文件</el-link
						>
					</el-descriptions-item>
				</el-descriptions>
			</div>
			<!-- 报价响应文件 -->
			<div class="form-section">
				<div class="label-header">报价响应文件</div>
				<el-table
					:data="detail?.quotationFiles"
					style="width: 100%"
				>
					<el-table-column
						label="序号"
						type="index"
						width="60"
					/>
					<el-table-column
						label="条件名称"
						prop="name"
					/>
					<el-table-column
						label="条件内容"
						prop="content"
					/>
				</el-table>
			</div>
		</div>
		<template #footer>
			<div class="flex justify-end gap-4">
				<el-button
					@click="onReject"
					type="danger"
					:loading="isLoading"
					>驳回</el-button
				>
				<el-button
					@click="onApprove"
					type="primary"
					:loading="isLoading"
					>通过</el-button
				>
			</div>
		</template>
	</el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from 'vue';
import type { DetailData } from './types';

const props = defineProps<{ visible: boolean; detail: DetailData | null }>();
const emit = defineEmits(['update:visible', 'approve', 'reject']);

const visible = ref(props.visible);
const isLoading = ref(false);
const formRef = ref();
const auditForm = ref({
	remark: '',
});

watch(
	() => props.visible,
	(v) => (visible.value = v)
);
watch(visible, (v) => emit('update:visible', v));

function onApprove() {
	isLoading.value = true;
	emit('approve', { ...props.detail, auditRemark: auditForm.value.remark });
	setTimeout(() => {
		isLoading.value = false;
		visible.value = false;
	}, 1000);
}

function onReject() {
	isLoading.value = true;
	emit('reject', { ...props.detail, auditRemark: auditForm.value.remark });
	setTimeout(() => {
		isLoading.value = false;
		visible.value = false;
	}, 1000);
}
</script>

<style scoped lang="scss">
@import '../../../styles/collapse-panel.scss';
:deep(.el-drawer) {
	.el-drawer__body {
		padding: 0 24px !important;
	}
}

.form-section {
	border-bottom: 1px solid var(--Color-Border-border-color-light, #ebeef5);
	&:last-child {
		border-bottom: none;
	}
}
</style>
