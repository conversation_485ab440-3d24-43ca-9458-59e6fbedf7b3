<template>
	<el-drawer
		v-model="visible"
		:title="'报名'"
		size="60vw"
		:with-header="true"
		custom-class="custom-drawer"
	>
		<el-form
			:model="form"
			:rules="rules"
			ref="formRef"
			label-width="145px"
		>
			<!-- 企业基础资料 -->
			<div class="form-section">
				<div class="label-header">企业基础资料</div>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item
							label="企业名称"
							prop="company.name"
						>
							<el-input
								v-model="form.company.name"
								placeholder="请输入企业名称"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item
							label="统一社会信用代码"
							prop="company.creditCode"
						>
							<el-input
								v-model="form.company.creditCode"
								placeholder="请输入统一社会信用代码"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item
							label="成立日期"
							prop="company.registerDate"
						>
							<el-date-picker
								v-model="form.company.registerDate"
								type="date"
								placeholder="请选择成立日期"
								style="width: 100%"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item
							label="注册资本"
							prop="company.registerCapital"
						>
							<el-input
								v-model="form.company.registerCapital"
								placeholder="请输入注册资本"
								suffix="元"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item
							label="法人代表"
							prop="company.legalPerson"
						>
							<el-input
								v-model="form.company.legalPerson"
								placeholder="请输入法人代表"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item
							label="详细地址"
							prop="company.address"
						>
							<el-input
								v-model="form.company.address"
								placeholder="请输入详细地址"
							/>
						</el-form-item>
					</el-col>
				</el-row>
			</div>
			<!-- 联系人信息 -->
			<div class="form-section">
				<div class="label-header">联系人信息</div>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item
							label="联系人"
							prop="contact.name"
						>
							<el-input
								v-model="form.contact.name"
								placeholder="请输入联系人"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item
							label="联系电话"
							prop="contact.phone"
						>
							<el-input
								v-model="form.contact.phone"
								placeholder="请输入联系电话"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item
							label="联系邮箱"
							prop="contact.email"
						>
							<el-input
								v-model="form.contact.email"
								placeholder="请输入联系邮箱"
							/>
						</el-form-item>
					</el-col>
				</el-row>
			</div>
			<!-- 预审材料 -->
			<div class="form-section">
				<div class="label-header">预审材料</div>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item
							label="1号证件"
							prop="preAudit.idCard"
						>
							<el-upload
								v-model:file-list="form.preAudit.idCard"
								action="#"
								:limit="1"
								:auto-upload="false"
								list-type="text"
								accept=".jpg,.png"
							>
								<el-button type="primary">Click to upload</el-button>
								<template #tip>
									<div class="el-upload__tip">jpg/png files with a size less than 500KB.</div>
								</template>
							</el-upload>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item
							label="业绩证件"
							prop="preAudit.businessLicense"
						>
							<el-upload
								v-model:file-list="form.preAudit.businessLicense"
								action="#"
								:limit="1"
								:auto-upload="false"
								list-type="text"
								accept=".jpg,.png"
							>
								<el-button type="primary">Click to upload</el-button>
								<template #tip>
									<div class="el-upload__tip">jpg/png files with a size less than 500KB.</div>
								</template>
							</el-upload>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item
							label="其它证件"
							prop="preAudit.other"
						>
							<el-upload
								v-model:file-list="form.preAudit.other"
								action="#"
								:limit="1"
								:auto-upload="false"
								list-type="text"
								accept=".jpg,.png"
							>
								<el-button type="primary">Click to upload</el-button>
								<template #tip>
									<div class="el-upload__tip">jpg/png files with a size less than 500KB.</div>
								</template>
							</el-upload>
						</el-form-item>
					</el-col>
				</el-row>
			</div>
			<!-- 报价响应文件 -->
			<div class="form-section">
				<div class="label-header">报价响应文件</div>
				<el-table
					:data="form.quotationFiles"
					style="width: 100%"
				>
					<el-table-column
						label="序号"
						type="index"
						width="60"
					/>
					<el-table-column
						label="条件名称"
						prop="name"
					/>
					<el-table-column
						label="条件内容"
						prop="content"
					/>
					<el-table-column
						label="操作"
						width="80"
					>
						<template #default="{ $index }">
							<el-link
								type="primary"
								@click="onEditQuotation($index)"
								>响应</el-link
							>
						</template>
					</el-table-column>
				</el-table>
			</div>
		</el-form>
		<template #footer>
			<div class="flex justify-end gap-4 dark:border-gray-700">
				<el-button
					@click="onSubmit"
					type="primary"
					:loading="isLoading"
					>提交</el-button
				>
			</div>
		</template>
	</el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from 'vue';
import type { SubmitForm } from './types';

const props = defineProps<{ visible: boolean }>();
const emit = defineEmits(['submit', 'update:visible']);

const visible = ref(props.visible);
watch(
	() => props.visible,
	(v) => (visible.value = v)
);
watch(visible, (v) => emit('update:visible', v));

const isLoading = ref(false);
const formRef = ref();
const form = ref<SubmitForm>({
	company: {
		name: '',
		creditCode: '',
		registerDate: '',
		registerCapital: '',
		legalPerson: '',
		address: '',
	},
	contact: {
		name: '',
		phone: '',
		email: '',
	},
	preAudit: {
		idCard: [],
		businessLicense: [],
		other: [],
	},
	quotationFiles: [
		{ name: '违约责任', content: '表行内容' },
		{ name: '交货时间', content: '表行内容' },
		{ name: '付款方式', content: '表行内容' },
	],
});
const rules = {
	'company.name': [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
	'company.creditCode': [{ required: true, message: '请输入统一社会信用代码', trigger: 'blur' }],
	'company.registerDate': [{ required: true, message: '请选择成立日期', trigger: 'change' }],
	'company.registerCapital': [{ required: true, message: '请输入注册资本', trigger: 'blur' }],
	'company.legalPerson': [{ required: true, message: '请输入法人代表', trigger: 'blur' }],
	'company.address': [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
	'contact.name': [{ required: true, message: '请输入联系人', trigger: 'blur' }],
	'contact.phone': [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
	'contact.email': [{ required: true, message: '请输入联系邮箱', trigger: 'blur' }],
	'preAudit.idCard': [{ required: true, message: '请上传1号证件', trigger: 'change' }],
	'preAudit.businessLicense': [{ required: true, message: '请上传业绩证件', trigger: 'change' }],
};

function onSubmit() {
	formRef.value?.validate((valid: boolean) => {
		if (valid) {
			isLoading.value = true;
			emit('submit', form.value);
			setTimeout(() => {
				isLoading.value = false;
				emit('update:visible', false);
			}, 1000);
		}
	});
}
function onEditQuotation(index: number) {
	// 响应报价内容编辑弹窗逻辑
}
</script>

<style scoped lang="scss">
@import '../../../styles/collapse-panel.scss';
:deep(.el-drawer) {
	.el-drawer__body {
		padding: 0 24px !important;
	}
}

.form-section {
	border-bottom: 1px solid var(--Color-Border-border-color-light, #ebeef5);
	&:last-child {
		border-bottom: none;
	}
}
</style>
